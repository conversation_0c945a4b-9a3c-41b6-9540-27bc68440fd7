#!/usr/bin/env python3
"""
简单测试URDF解析和关节控制功能
"""

import sys
import os
from pathlib import Path

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from urdf_viewer_standalone import SimpleURDFParser

def test_urdf_parsing():
    """测试URDF解析功能"""
    print("测试URDF解析和关节控制功能")
    print("=" * 50)
    
    # 创建解析器
    parser = SimpleURDFParser()
    
    # 测试URDF文件路径
    urdf_path = Path(__file__).parent.parent / "data" / "auboi5" / "aubo_i5.urdf"
    
    if not urdf_path.exists():
        print(f"❌ URDF文件不存在: {urdf_path}")
        return False
    
    print(f"📁 URDF文件路径: {urdf_path}")
    
    # 解析URDF
    if parser.parse_urdf(str(urdf_path)):
        print("✅ URDF解析成功!")
        print(f"🤖 机器人名称: {parser.robot_name}")
        print(f"🔗 链接数量: {len(parser.links)}")
        print(f"⚙️  关节数量: {len(parser.joints)}")
        
        # 分析关节类型
        movable_joints = []
        fixed_joints = []
        
        for joint in parser.joints:
            if joint['type'] in ['revolute', 'continuous', 'prismatic']:
                movable_joints.append(joint)
            else:
                fixed_joints.append(joint)
        
        print(f"\n🎮 可控制关节 ({len(movable_joints)}):")
        for i, joint in enumerate(movable_joints, 1):
            print(f"  {i}. {joint['name']} ({joint['type']})")
            if 'limit' in joint:
                limit = joint['limit']
                print(f"     范围: {limit['lower']:.3f} ~ {limit['upper']:.3f}")
            if 'axis' in joint:
                axis = joint['axis']
                print(f"     轴向: [{axis[0]:.1f}, {axis[1]:.1f}, {axis[2]:.1f}]")
        
        print(f"\n🔒 固定关节 ({len(fixed_joints)}):")
        for i, joint in enumerate(fixed_joints, 1):
            print(f"  {i}. {joint['name']} ({joint['type']})")
        
        print(f"\n🎉 关节控制面板功能已实现!")
        print(f"   - 将为 {len(movable_joints)} 个可动关节创建控制器")
        print(f"   - 每个控制器包含滑块和数值输入框")
        print(f"   - 支持实时更新机器人姿态")
        print(f"   - 包含重置关节角度功能")
        
        # 测试正运动学计算
        print(f"\n🧮 测试正运动学计算:")
        test_joint_values = {}
        for joint in movable_joints[:3]:  # 测试前3个关节
            test_joint_values[joint['name']] = 0.5  # 设置测试角度
        
        print(f"   测试关节角度: {test_joint_values}")
        
        # 测试变换链计算
        for link in parser.links[:2]:  # 测试前2个链接
            transform_chain = parser.get_transform_chain(link['name'], test_joint_values)
            print(f"   链接 '{link['name']}' 的变换链长度: {len(transform_chain)}")
        
        return True
    else:
        print("❌ URDF解析失败")
        return False

if __name__ == "__main__":
    success = test_urdf_parsing()
    if success:
        print(f"\n✨ 所有功能测试通过!")
        print(f"💡 提示: 运行 'python urdf_viewer_standalone.py' 查看完整的GUI界面")
    else:
        print(f"\n❌ 测试失败")
    
    sys.exit(0 if success else 1)
