# 25AutoAssembly 主程序打包脚本
# 使用PyInstaller打包Python应用程序

param(
    [switch]$Clean,
    [switch]$Debug,
    [switch]$Test
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 获取脚本目录
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $ScriptDir

Write-Host "🚀 开始打包25AutoAssembly主程序..." -ForegroundColor Green
Write-Host "📁 项目目录: $ScriptDir" -ForegroundColor Cyan

# 检查conda环境
Write-Host "🔍 检查conda环境..." -ForegroundColor Yellow
try {
    $condaInfo = conda info --envs 2>$null
    if ($condaInfo -match "25autoassembly") {
        Write-Host "✅ 找到conda环境: 25autoassembly" -ForegroundColor Green
    } else {
        Write-Host "❌ 未找到conda环境: 25autoassembly" -ForegroundColor Red
        Write-Host "请先创建conda环境: conda create -n 25autoassembly python=3.9" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "❌ conda命令不可用，请确保已安装Anaconda/Miniconda" -ForegroundColor Red
    exit 1
}

# 激活conda环境
Write-Host "🔄 激活conda环境..." -ForegroundColor Yellow
try {
    $env:CONDA_DEFAULT_ENV = "25autoassembly"
    Write-Host "✅ conda环境已设置" -ForegroundColor Green
} catch {
    Write-Host "❌ 激活conda环境时出错: $_" -ForegroundColor Red
    exit 1
}

# 检查PyInstaller
Write-Host "🔍 检查PyInstaller..." -ForegroundColor Yellow
try {
    $pyinstallerVersion = & python -c "import PyInstaller; print(PyInstaller.__version__)" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ PyInstaller版本: $pyinstallerVersion" -ForegroundColor Green
    } else {
        Write-Host "❌ PyInstaller未安装，正在安装..." -ForegroundColor Yellow
        & pip install pyinstaller
        if ($LASTEXITCODE -ne 0) {
            Write-Host "❌ PyInstaller安装失败" -ForegroundColor Red
            exit 1
        }
        Write-Host "✅ PyInstaller安装成功" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ 检查PyInstaller时出错: $_" -ForegroundColor Red
    exit 1
}

# 清理旧的构建文件
if ($Clean) {
    Write-Host "🧹 清理旧的构建文件..." -ForegroundColor Yellow
    if (Test-Path "build") { Remove-Item -Recurse -Force "build" }
    if (Test-Path "dist") { Remove-Item -Recurse -Force "dist" }
    if (Test-Path "__pycache__") { Remove-Item -Recurse -Force "__pycache__" }
    Get-ChildItem -Recurse -Name "__pycache__" | ForEach-Object { Remove-Item -Recurse -Force $_ }
    Write-Host "✅ 清理完成" -ForegroundColor Green
}

# 验证必要文件
Write-Host "🔍 验证项目文件..." -ForegroundColor Yellow
$requiredFiles = @("main.py", "main.spec")
$requiredDirs = @("API", "config", "data_models", "device_communication", "file_handler", "gui", "resource", "utils")

foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) {
        Write-Host "❌ 缺少必要文件: $file" -ForegroundColor Red
        exit 1
    }
}

foreach ($dir in $requiredDirs) {
    if (-not (Test-Path $dir)) {
        Write-Host "❌ 缺少必要目录: $dir" -ForegroundColor Red
        exit 1
    }
}
Write-Host "✅ 项目文件验证通过" -ForegroundColor Green

# 开始打包
Write-Host "📦 开始PyInstaller打包..." -ForegroundColor Yellow
$buildArgs = @("--clean", "--noconfirm")
if ($Debug) {
    $buildArgs += "--debug=all"
    Write-Host "🐛 调试模式已启用" -ForegroundColor Cyan
}

try {
    & pyinstaller @buildArgs main.spec
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ PyInstaller打包失败" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ PyInstaller打包成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 打包过程中出错: $_" -ForegroundColor Red
    exit 1
}

# 验证打包结果
Write-Host "🔍 验证打包结果..." -ForegroundColor Yellow
$exePath = "dist\25AutoAssembly\25AutoAssembly.exe"
if (Test-Path $exePath) {
    $fileSize = (Get-Item $exePath).Length / 1MB
    Write-Host "✅ 可执行文件已生成: $exePath" -ForegroundColor Green
    Write-Host "📊 文件大小: $([math]::Round($fileSize, 2)) MB" -ForegroundColor Cyan
} else {
    Write-Host "❌ 可执行文件未生成" -ForegroundColor Red
    exit 1
}

# 测试运行
if ($Test) {
    Write-Host "🧪 测试运行可执行文件..." -ForegroundColor Yellow
    try {
        Start-Process -FilePath $exePath -WindowStyle Normal
        Write-Host "✅ 程序启动测试成功" -ForegroundColor Green
        Write-Host "⚠️  请手动验证程序功能是否正常" -ForegroundColor Yellow
    } catch {
        Write-Host "❌ 程序启动测试失败: $_" -ForegroundColor Red
    }
}

Write-Host "🎉 打包完成！" -ForegroundColor Green
Write-Host "📁 输出目录: dist\25AutoAssembly\" -ForegroundColor Cyan
Write-Host "🚀 可执行文件: $exePath" -ForegroundColor Cyan

# 显示使用说明
Write-Host "`n📋 使用说明:" -ForegroundColor Yellow
Write-Host "   - 直接运行: .\dist\25AutoAssembly\25AutoAssembly.exe" -ForegroundColor White
Write-Host "   - 清理重新打包: .\main.ps1 -Clean" -ForegroundColor White
Write-Host "   - 调试模式打包: .\main.ps1 -Debug" -ForegroundColor White
Write-Host "   - 打包并测试: .\main.ps1 -Test" -ForegroundColor White
