# tcp_server.py
from PySide6.QtCore import QObject, Signal
import socket
import threading
import logging

from common.settings import A_IP, B_IP

# 基于 IP 的身份映射（可替换为配置文件加载）
IP_IDENTITY_MAP = {
    A_IP: "A"
}

class TCPServer(QObject):
    message_received = Signal(str, str, str)  # 来源(A), IP, 消息

    def __init__(self, ip, port):
        super().__init__()
        self.local_ip = ip
        self.local_port = int(port)
        self.server_socket = None
        self.client_a = None
        self.addr_a = None
        self.is_running = False
        self.lock = threading.Lock()

    def start(self):
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.local_ip, self.local_port))
            self.server_socket.listen(5)
            logging.info(f"TCP 监听启动: {self.local_ip}:{self.local_port}")

            self.is_running = True
            threading.Thread(target=self._accept_loop, daemon=True).start()
        except Exception as e:
            logging.error(f"启动失败: {e}")

    def _accept_loop(self):
        while self.is_running:
            try:
                client_sock, client_addr = self.server_socket.accept()
                logging.info(f"新连接来自: {client_addr}")
                threading.Thread(target=self._handle_client, args=(client_sock, client_addr), daemon=True).start()
            except Exception as e:
                logging.error(f"监听失败: {e}")

    def _handle_client(self, sock, addr):
        try:
            ip, _ = addr
            identity = IP_IDENTITY_MAP.get(ip, None)

            if identity == "A":
                with self.lock:
                    self.client_a = sock
                    self.addr_a = addr
                self._recv_from_a(sock, addr)
            else:
                logging.warning(f"未知IP {ip} 连接, 拒绝")
                sock.close()
        except Exception as e:
            logging.error(f"连接初始化失败: {e}")
            sock.close()

    def _recv_from_a(self, sock, addr): # a是张总那边的程序，作为客户端
        buffer = ""
        while self.is_running:
            try:
                data = sock.recv(1024)
                if not data:
                    break
                buffer += data.decode(errors='ignore')  # 允许忽略非法字符

                while "$Frame" in buffer and "$End" in buffer:
                    start = buffer.find("$Frame")
                    end = buffer.find("$End", start)
                    if end == -1:
                        break
                    frame_data = buffer[start + len("$Frame"):end]
                    buffer = buffer[end + len("$End"):]  # 清除已处理内容

                    msg = frame_data.strip()
                    logging.info(f"[A] 接收完整数据: {msg}")
                    self._handle_data(msg, "位置信息", addr[0])

            except Exception as e:
                logging.error(f"A 通信异常: {e}")
                break
        sock.close()
        self.client_a = None
        self.addr_a = None

    def _handle_data(self,sender, msg, ip):
        self.message_received.emit(sender, ip, msg)

    def _send(self,command): #调用这个发送函数向张总程序发送指令
        command = bytes(command, encoding='UTF-8')
        self.client_a.sendall(command)
        logging.info(f"运动指令{command}已发送")

    def stop(self):
        self.is_running = False
        if self.client_a:
            self.client_a.close()
        if self.server_socket:
            self.server_socket.close()
        logging.info("TCP 服务端已停止")