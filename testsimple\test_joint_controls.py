#!/usr/bin/env python3
"""
测试关节控制面板功能
即使没有VTK也能展示关节控制界面
"""

import sys
import os
from pathlib import Path

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QLabel, QPushButton
    from PyQt5.QtCore import Qt
    
    # 导入我们修改的URDF查看器
    from urdf_viewer_standalone import SimpleURDFParser, URDFVisualizerStandalone
    
    class JointControlTester(QMainWindow):
        """关节控制测试器"""
        
        def __init__(self):
            super().__init__()
            self.parser = SimpleURDFParser()
            self.init_ui()
            
        def init_ui(self):
            self.setWindowTitle("关节控制面板测试")
            self.setGeometry(100, 100, 800, 600)
            
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            layout = QVBoxLayout(central_widget)
            
            # 标题
            title = QLabel("关节控制面板功能测试")
            title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
            title.setAlignment(Qt.AlignCenter)
            layout.addWidget(title)
            
            # 说明
            info = QLabel("这个测试展示了新增的关节控制面板功能，即使没有VTK 3D渲染也能正常工作")
            info.setWordWrap(True)
            info.setAlignment(Qt.AlignCenter)
            layout.addWidget(info)
            
            # 测试按钮
            test_button = QPushButton("测试加载URDF文件")
            test_button.clicked.connect(self.test_urdf_loading)
            layout.addWidget(test_button)
            
            # 结果显示区域
            self.result_label = QLabel("点击按钮开始测试...")
            self.result_label.setWordWrap(True)
            self.result_label.setStyleSheet("background-color: #f0f0f0; padding: 10px; margin: 10px;")
            layout.addWidget(self.result_label)
            
        def test_urdf_loading(self):
            """测试URDF加载和关节解析"""
            try:
                # 测试URDF文件路径
                urdf_path = Path(__file__).parent.parent / "data" / "auboi5" / "aubo_i5.urdf"
                
                if not urdf_path.exists():
                    self.result_label.setText(f"URDF文件不存在: {urdf_path}")
                    return
                
                # 解析URDF
                if self.parser.parse_urdf(str(urdf_path)):
                    result = f"✅ 成功解析URDF文件!\n\n"
                    result += f"机器人名称: {self.parser.robot_name}\n"
                    result += f"链接数量: {len(self.parser.links)}\n"
                    result += f"关节数量: {len(self.parser.joints)}\n\n"
                    
                    # 分析关节类型
                    movable_joints = []
                    fixed_joints = []
                    
                    for joint in self.parser.joints:
                        if joint['type'] in ['revolute', 'continuous', 'prismatic']:
                            movable_joints.append(joint)
                        else:
                            fixed_joints.append(joint)
                    
                    result += f"可控制关节 ({len(movable_joints)}):\n"
                    for joint in movable_joints:
                        result += f"  • {joint['name']} ({joint['type']})\n"
                        if 'limit' in joint:
                            limit = joint['limit']
                            result += f"    范围: {limit['lower']:.3f} ~ {limit['upper']:.3f}\n"
                        if 'axis' in joint:
                            axis = joint['axis']
                            result += f"    轴向: [{axis[0]:.1f}, {axis[1]:.1f}, {axis[2]:.1f}]\n"
                    
                    result += f"\n固定关节 ({len(fixed_joints)}):\n"
                    for joint in fixed_joints:
                        result += f"  • {joint['name']} ({joint['type']})\n"
                    
                    result += f"\n🎉 关节控制面板将为 {len(movable_joints)} 个可动关节创建控制器!"
                    
                else:
                    result = "❌ URDF解析失败"
                
                self.result_label.setText(result)
                
            except Exception as e:
                self.result_label.setText(f"❌ 测试出错: {str(e)}")

def main():
    app = QApplication(sys.argv)

    print("关节控制面板功能测试")
    print("=" * 40)

    try:
        window = JointControlTester()
        window.show()
        sys.exit(app.exec_())
    except Exception as e:
        print(f"启动测试程序出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
