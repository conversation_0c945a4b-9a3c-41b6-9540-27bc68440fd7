<?xml version="1.0"?>
<robot name="aubo_i5">
  <dh_type type="modified"/>
  <safety_para singularity_consistent="0.02 0.01 10 0.2 0.045 10"/>
  <friction_coeff value="0.65"/>
  <link name="base_link">
    <inertial>
      <origin xyz="0.000129 0.001249 0.016678" rpy="0 0 0" />
      <mass value="1.53902" />
      <inertia ixx="0.003278" ixy="-0.000001" ixz="-0.000001" iyy="0.003017" iyz="-0.000048" izz="0.005841" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="meshes/collision/link0.STL" />
      </geometry>
      <material name="">
        <color rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="meshes/collision/link0.STL" />
      </geometry>
    </collision>
  </link>

  <link name="shoulder_Link">
    <inertial>
      <origin xyz="-0.00001 0.001807 -0.013854" rpy="0 0 0" />
      <mass value="4.848581" />
      <inertia ixx="0.011842" ixy="0" ixz="0.000004" iyy="0.0118" iyz="-0.000103" izz="0.008103" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="meshes/collision/link1.STL" />
      </geometry>
      <material name="">
        <color rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="meshes/collision/link1.STL" />
      </geometry>
    </collision>
  </link>

  <joint name="shoulder_joint" type="revolute">
    <origin xyz="0 0 0.122" rpy="0 0 3.1415926535897932384626433832795" />
    <parent link="base_link" />
    <child link="shoulder_Link" />
    <axis xyz="0 0 1" />
    <limit lower="-6.283185307179586476925286766559" upper="6.283185307179586476925286766559" effort="133" start_stop="207" velocity="2.5964" />
    <property inertia="2.027236783" damping="0" stiffness="0" offset="0" motor_constant="8.72" ratio="121" protect_max_torque="80.0" equa_inertia="1.5" collision_detection_para = "50 50 30 20"/>
  </joint>

  <link name="upperArm_Link">
    <inertial>
      <origin xyz="0.2040 0 0.006603" rpy="0 0 0" />
      <mass value="10.836261" />
      <inertia ixx="0.026024" ixy="0.00002" ixz="0" iyy="0.432805" iyz="0" izz="0.425155" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="meshes/collision/link2.STL" />
      </geometry>
      <material name="">
        <color rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="meshes/collision/link2.STL" />
      </geometry>
    </collision>
  </link>

  <joint name="upperArm_joint" type="revolute">
    <origin xyz="0 0.1215 0" rpy="-1.5707963267948966192313216916398 -1.5707963267948966192313216916398 0" />
    <parent link="shoulder_Link" />
    <child link="upperArm_Link" />
    <axis xyz="0 0 1" />
    <limit lower="-6.283185307179586476925286766559" upper="6.283185307179586476925286766559" effort="133" start_stop="207" velocity="2.5964" />
    <property inertia="2.027236783" damping="0" stiffness="0" offset="0" motor_constant="8.72" ratio="121" protect_max_torque="80.0" equa_inertia="1.5" collision_detection_para = "50 50 30 20"/>
  </joint>

  <link name="foreArm_Link">
    <inertial>
      <origin xyz="0.264626 -0.00002 0.093611" rpy="0 0 0" />
      <mass value="2.849168" />
      <inertia ixx="0.003634" ixy="0.000007" ixz="0.000168" iyy="0.065327" iyz="0.000002" izz="0.064676" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="meshes/collision/link3.STL" />
      </geometry>
      <material name="">
        <color rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="meshes/collision/link3.STL" />
      </geometry>
    </collision>
  </link>

  <joint name="foreArm_joint" type="revolute">
    <origin xyz="0.408 0 0" rpy="-3.1415926535897932384626433832795 0 0" />
    <parent link="upperArm_Link" />
    <child link="foreArm_Link" />
    <axis xyz="0 0 1" />
    <limit lower="-6.283185307179586476925286766559" upper="6.283185307179586476925286766559" effort="133" start_stop="207" velocity="2.5964" />
    <property inertia="2.027236783" damping="0" stiffness="0" offset="0" motor_constant="8.72" ratio="121" protect_max_torque="60.0" equa_inertia="1.2" collision_detection_para = "50 50 30 20"/>
  </joint>

  <link name="wrist1_Link">
    <inertial>
      <origin xyz="0.000038 0.011278 -0.001539" rpy="0 0 0" />
      <mass value="1.628022" />
      <inertia ixx="0.001951" ixy="0.000003" ixz="0.000001" iyy="0.001154" iyz="-0.000042" izz="0.001931" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="meshes/collision/link4.STL" />
      </geometry>
      <material name="">
        <color rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="meshes/collision/link4.STL" />
      </geometry>
    </collision>
  </link>

  <joint name="wrist1_joint" type="revolute">
    <origin xyz="0.376 0 0" rpy="3.1415926535897932384626433832795 0 1.5707963267948966192313216916398" />
    <parent link="foreArm_Link" />
    <child link="wrist1_Link" />
    <axis xyz="0 0 1" />
    <limit lower="-6.283185307179586476925286766559" upper="6.283185307179586476925286766559" effort="13.5" start_stop="34" velocity="3.1105" />
    <property inertia="0.219280696" damping="0" stiffness="0" offset="0" motor_constant="7.092" ratio="101" protect_max_torque="16.0" equa_inertia="0.05" collision_detection_para = "50 50 30 20"/>
  </joint>

  <link name="wrist2_Link">
    <inertial>
      <origin xyz="-0.000038 -0.011278 -0.001539" rpy="0 0 0" />
      <mass value="1.628022" />
      <inertia ixx="0.001951" ixy="0.000003" ixz="-0.000001" iyy="0.001154" iyz="0.000042" izz="0.001931" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="meshes/collision/link5.STL" />
      </geometry>
      <material name="">
        <color rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="meshes/collision/link5.STL" />
      </geometry>
    </collision>
  </link>

  <joint name="wrist2_joint" type="revolute">
    <origin xyz="0 0.1025 0" rpy="-1.5707963267948966192313216916398 0 0" />
    <parent link="wrist1_Link" />
    <child link="wrist2_Link" />
    <axis xyz="0 0 1" />
    <limit lower="-6.283185307179586476925286766559" upper="6.283185307179586476925286766559" effort="13.5" start_stop="34" velocity="3.1105" />
    <property inertia="0.219280696" damping="0" stiffness="0" offset="0" motor_constant="7.092" ratio="101" protect_max_torque="16.0" equa_inertia="0.05" collision_detection_para = "50 50 30 20"/>
  </joint>

  <link name="wrist3_Link">
    <inertial>
      <origin xyz="0.000022 0.0006 -0.017081" rpy="0 0 0" />
      <mass value="0.1978" />
      <inertia ixx="0.000118" ixy="0.0" ixz="0" iyy="0.000112" iyz="0.000001" izz="0.000184" />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="meshes/collision/link6.STL" />
      </geometry>
      <material name="">
        <color rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="meshes/collision/link6.STL" />
      </geometry>
    </collision>
  </link>

  <joint name="wrist3_joint" type="revolute">
    <origin xyz="0 -0.094 0" rpy="1.5707963267948966192313216916398 0 0" />
    <parent link="wrist2_Link" />
    <child link="wrist3_Link" />
    <axis xyz="0 0 1" />
    <limit lower="-6.283185307179586476925286766559" upper="6.283185307179586476925286766559" effort="13.5" start_stop="34" velocity="3.1105" />
    <property inertia="0.219280696" damping="0" stiffness="0" offset="0" motor_constant="7.092" ratio="101" protect_max_torque="10.0" equa_inertia="0.01" collision_detection_para = "50 50 30 20"/>
  </joint>

  <link name="world" />

  <joint name="world_joint" type="fixed">
    <parent link="world" />
    <child link = "base_link" />
    <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0" />
  </joint>
</robot>
