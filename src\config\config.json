{"device": {"motion_control_server": {"ip": "************", "port": 8080, "timeout": 5.0, "retry_count": 3}, "device_mapping_file": "config/device_mapping.json"}, "file_settings": {"result_file_path": "d:\\Program\\python_project\\25AutoAssembly\\resource\\result.txt", "auto_reload": true, "file_check_interval": 1.0}, "motion_control": {"translation_rotation_ratio": 4, "motion_level": 0.001, "enable_safety_limits": true}, "zfr_mapping": {"zfr_3_axis": "Z", "zfr_6_axes": ["X", "Y_rotation"], "zfr_7_axes": ["Y", "X_rotation"]}, "gui": {"window_title": "25AutoAssembly - 自动装配控制系统", "window_size": "800x600", "theme": "default"}, "logging": {"level": "INFO", "file_path": "logs/system.log", "max_file_size": 10485760, "backup_count": 5}}