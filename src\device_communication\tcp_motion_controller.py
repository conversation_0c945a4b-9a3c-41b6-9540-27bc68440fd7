#!/usr/bin/env python3
"""
25AutoAssembly TCP运动控制器
通过TCP连接到运动控制计算机，发送带设备IP的运动指令
"""

import socket
import json
import threading
import time
import logging
from typing import Optional, Dict, Any, Callable
from dataclasses import dataclass
from enum import Enum
from PyQt5.QtCore import QObject, pyqtSignal as Signal, QTimer, QMetaObject, Qt

logger = logging.getLogger(__name__)

def get_all_local_ips():
    """获取本机所有有效的IP地址"""
    ip_list = []

    try:
        import netifaces

        # 获取所有网络接口
        interfaces = netifaces.interfaces()

        for interface in interfaces:
            # 获取接口的地址信息
            addrs = netifaces.ifaddresses(interface)

            # 检查IPv4地址
            if netifaces.AF_INET in addrs:
                for addr_info in addrs[netifaces.AF_INET]:
                    ip = addr_info.get('addr')
                    if ip and ip != '127.0.0.1':  # 排除回环地址
                        # 检查是否为有效的私有网络地址或公网地址
                        if (ip.startswith('192.168.') or
                            ip.startswith('10.') or
                            ip.startswith('172.') or
                            not ip.startswith('169.254.')):  # 排除APIPA地址
                            ip_list.append(ip)

        logger.info(f"使用netifaces获取到 {len(ip_list)} 个IP地址")

    except ImportError:
        logger.info("netifaces库未安装，使用备用方法获取IP地址")
        # 如果netifaces不可用，使用备用方法
        ip_list = get_local_ips_fallback()
    except Exception as e:
        logger.warning(f"获取网络接口信息失败: {e}")
        ip_list = get_local_ips_fallback()

    # 去重并排序
    ip_list = sorted(list(set(ip_list)))

    # 如果没有找到任何IP，添加默认值
    if not ip_list:
        ip_list = ['127.0.0.1']

    return ip_list


def get_local_ips_fallback():
    """备用方法获取本机IP地址列表"""
    import socket

    ip_list = []

    try:
        # 方法1: 通过连接外部地址获取主要IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            primary_ip = s.getsockname()[0]
            if primary_ip and primary_ip != '127.0.0.1':
                ip_list.append(primary_ip)
    except Exception:
        pass

    try:
        # 方法2: 获取主机名对应的IP地址
        hostname = socket.gethostname()
        host_ips = socket.gethostbyname_ex(hostname)[2]
        for ip in host_ips:
            if ip and ip != '127.0.0.1' and not ip.startswith('169.254.'):
                ip_list.append(ip)
    except Exception:
        pass

    # 去重
    ip_list = list(set(ip_list))

    # 如果仍然没有找到，添加localhost
    if not ip_list:
        ip_list = ['127.0.0.1']

    return ip_list


def get_local_ip():
    """获取本机主要IP地址（保持向后兼容）"""
    ip_list = get_all_local_ips()
    return ip_list[0] if ip_list else "127.0.0.1"


class DeviceType(Enum):
    """硬件设备类型"""
    SUBMIRROR = ("次镜", "SUBMIRROR")
    MAINMIRROR = ("主镜", "MAINMIRROR")
    TERTIARY = ("三镜", "TERTIARY")
    
    def __init__(self, display_name: str, type_name: str):
        self.display_name = display_name
        self.type_name = type_name
    
    @property
    def ip_address(self) -> str:
        """动态获取IP地址（从配置文件）"""
        from src.config.device_config import device_config
        device_id = device_config.get_device_id_by_type(self.type_name)
        if device_id:
            return device_config.get_device_ip(device_id) or "***********"
        return "***********"





@dataclass
class DeviceStatus:
    """设备状态数据"""
    device_ip: str
    connected: bool
    position: Dict[str, float]
    last_update: float


class TCPMotionController(QObject):
    """
    TCP运动控制器
    连接到运动控制计算机，发送运动指令
    """
    
    # 信号定义
    connection_changed = Signal(bool)  # 连接状态变化
    device_selected = Signal(str, str)  # 设备选择变化 (设备名, IP)
    device_status_updated = Signal(str, dict)  # 设备状态更新
    command_sent = Signal(str, dict)  # 命令发送成功
    error_occurred = Signal(str)  # 错误发生
    pose_data_received = Signal(dict)  # 位姿数据接收信号
    query_result_received = Signal(list)  # 查询结果接收信号 (M1字段列表)
    
    def __init__(self, host: str = None, port: int = 5050):
        """
        初始化TCP运动控制器 (服务器模式)

        Args:
            host: 本机IP地址
            port: 监听端口
        """
        super().__init__()

        self.host = host or get_local_ip()  # 自动获取本机IP
        self.port = port
        self.server_socket = None  # 服务器套接字
        self.client_socket = None  # 客户端连接套接字
        self.connected = False  # 客户端连接状态
        self.server_running = False  # 服务器运行状态
        
        # 加载设备配置
        from src.config.device_config import device_config
        self.device_config = device_config
        
        # 当前选择的设备
        self.current_device = DeviceType.SUBMIRROR
        
        # 设备状态
        self.device_statuses: Dict[str, DeviceStatus] = {}
        
        # 服务器线程
        self.server_thread = None
        self.receive_thread = None
        self.running = False

        # 接收缓冲区
        self.buffer = ""

        # 心跳定时器 (已禁用 - 客户端不需要心跳包)
        self.heartbeat_timer = QTimer()
        self.heartbeat_timer.timeout.connect(self._send_heartbeat)
        self.heartbeat_timer.setInterval(5000)  # 5秒心跳
        self.heartbeat_enabled = False  # 禁用心跳功能
        
        # 初始化设备状态
        self._init_device_statuses()
    
    def _init_device_statuses(self) -> None:
        """初始化设备状态"""
        for device_type in DeviceType:
            self.device_statuses[device_type.ip_address] = DeviceStatus(
                device_ip=device_type.ip_address,
                connected=False,
                position={"x": 0.0, "y": 0.0, "z": 0.0, "rx": 0.0, "ry": 0.0, "rz": 0.0},
                last_update=0.0
            )
    
    def start_server(self, host: str = None, port: int = None) -> bool:
        """
        启动TCP服务器，等待运动控制计算机连接

        Args:
            host: 监听IP地址
            port: 监听端口

        Returns:
            服务器启动是否成功
        """
        try:
            if self.server_running:
                logger.info("TCP服务器已经在运行")
                return True

            if host:
                self.host = host
            if port:
                self.port = port

            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

            logger.info(f"正在启动TCP服务器: {self.host}:{self.port}")
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(1)  # 只允许一个客户端连接

            self.server_running = True
            self.running = True

            # 启动服务器线程
            self.server_thread = threading.Thread(target=self._server_loop, daemon=True)
            self.server_thread.start()

            logger.info("TCP服务器启动成功，等待客户端连接")
            return True

        except Exception as e:
            logger.error(f"启动TCP服务器失败: {e}")
            self.server_running = False
            self.error_occurred.emit(f"服务器启动失败: {e}")
            return False
    
    def stop_server(self) -> None:
        """停止TCP服务器"""
        try:
            self.running = False
            self.server_running = False

            # 心跳已禁用，无需停止
            # if self.heartbeat_timer.isActive():
            #     QMetaObject.invokeMethod(self.heartbeat_timer, "stop", Qt.QueuedConnection)

            # 关闭客户端连接
            if self.client_socket:
                self.client_socket.close()
                self.client_socket = None

            # 关闭服务器套接字
            if self.server_socket:
                self.server_socket.close()
                self.server_socket = None
            
            if self.receive_thread and self.receive_thread.is_alive():
                self.receive_thread.join(timeout=1.0)
            
            self.connected = False
            logger.info("运动控制计算机连接已断开")
            self.connection_changed.emit(False)
            
        except Exception as e:
            logger.error(f"断开连接异常: {e}")
    
    def select_device(self, device_type: DeviceType) -> None:
        """
        选择当前控制的设备
        
        Args:
            device_type: 设备类型
        """
        self.current_device = device_type
        logger.info(f"已选择设备: {device_type.display_name} ({device_type.ip_address})")
        self.device_selected.emit(device_type.display_name, device_type.ip_address)
    
    def get_current_device(self) -> DeviceType:
        """获取当前选择的设备"""
        return self.current_device
    
    def send_motion_command(self, dx: float = 0, dy: float = 0, dz: float = 0,
                           drx: float = 0, dry: float = 0, drz: float = 0,
                           target_device: Optional[DeviceType] = None) -> bool:
        """
        发送运动指令
        
        Args:
            dx, dy, dz: X, Y, Z轴相对移动距离
            drx, dry, drz: RX, RY, RZ轴相对旋转角度
            target_device: 目标设备，None表示使用当前选择的设备
            
        Returns:
            发送是否成功
        """
        if not self.connected:
            self.error_occurred.emit("未连接到运动控制计算机")
            return False

        device = target_device or self.current_device
        device_ip = device.ip_address

        # 格式化新的指令格式: $$$$IP + MOV + 6轴参数
        command_str = f"$$$${device_ip}MOV X{dx:.6f} Y{dy:.6f} Z{dz:.6f} A{drx:.6f} B{dry:.6f} C{drz:.6f}"

        try:
            # 发送指令
            self.client_socket.send(command_str.encode('utf-8'))

            # 记录发送的指令
            self.command_sent.emit(device_ip, {
                "command": command_str,
                "timestamp": time.time()
            })

            logger.info(f"运动指令已发送: {command_str}")
            return True

        except Exception as e:
            logger.error(f"发送运动指令失败: {e}")
            self.error_occurred.emit(f"发送运动指令失败: {e}")
            return False
    

    
    def stop_motion(self, target_device: Optional[DeviceType] = None) -> bool:
        """
        停止运动
        
        Args:
            target_device: 目标设备，None表示停止当前设备
            
        Returns:
            发送是否成功
        """
        if not self.connected:
            self.error_occurred.emit("未连接到运动控制计算机")
            return False

        device = target_device or self.current_device
        device_ip = device.ip_address

        # 格式化停止指令: $$$$IP + STP
        command_str = f"$$$${device_ip}STP"

        try:
            # 发送指令
            self.client_socket.send(command_str.encode('utf-8'))

            # 记录发送的指令
            self.command_sent.emit(device_ip, {
                "command": command_str,
                "timestamp": time.time()
            })

            logger.info(f"停止指令已发送: {command_str}")
            return True

        except Exception as e:
            logger.error(f"发送停止指令失败: {e}")
            self.error_occurred.emit(f"发送停止指令失败: {e}")
            return False

    def query_device_pose(self, target_device: Optional[DeviceType] = None) -> bool:
        """
        查询设备当前位姿

        Args:
            target_device: 目标设备，None表示使用当前选择的设备

        Returns:
            发送是否成功
        """
        if not self.connected:
            self.error_occurred.emit("未连接到运动控制计算机")
            return False

        device = target_device or self.current_device
        device_ip = device.ip_address

        # 格式化位姿查询指令: $$$$IP + QUERY
        command_str = f"$$$${device_ip}QUERY"

        try:
            # 发送指令
            self.client_socket.send(command_str.encode('utf-8'))

            # 记录发送的指令
            self.command_sent.emit(device_ip, {
                "command": command_str,
                "timestamp": time.time()
            })

            logger.info(f"位姿查询指令已发送: {command_str}")
            return True

        except Exception as e:
            logger.error(f"发送位姿查询指令失败: {e}")
            self.error_occurred.emit(f"发送位姿查询指令失败: {e}")
            return False

    def refresh_pose_data(self) -> bool:
        """
        刷新位姿数据 - 发送特殊查询指令触发客户端开始发送实时位姿数据

        Returns:
            指令发送是否成功
        """
        if not self.connected:
            self.error_occurred.emit("未连接到运动控制计算机")
            return False

        # 获取当前选择设备的IP
        current_device_ip = self.current_device.ip_address
        if not current_device_ip:
            self.error_occurred.emit("当前设备IP获取失败")
            return False

        try:
            # 发送特殊查询指令到当前选择的设备: $$$$当前设备IP+MOV X0
            command_str = f"$$$${current_device_ip}MOV X0"

            # 发送指令
            self.client_socket.send(command_str.encode('utf-8'))

            # 记录发送的指令
            self.command_sent.emit(current_device_ip, {
                "command": command_str,
                "timestamp": time.time()
            })

            logger.info(f"位姿刷新指令已发送到 {self.current_device.display_name}: {command_str}")
            return True

        except Exception as e:
            logger.error(f"发送位姿刷新指令失败: {e}")
            self.error_occurred.emit(f"发送位姿刷新指令失败: {e}")
            return False

    def stop_all_devices(self) -> bool:
        """
        停止所有设备运动

        Returns:
            发送是否成功
        """
        success_count = 0
        for device_type in DeviceType:
            if self.stop_motion(device_type):
                success_count += 1

        return success_count == len(DeviceType)

    def query_devices(self) -> bool:
        """
        查询设备状态 - 向***********发送查询指令

        Returns:
            发送是否成功
        """
        if not self.connected:
            self.error_occurred.emit("未连接到运动控制计算机")
            return False

        # 向***********发送查询指令
        query_ip = "***********"
        command_str = f"$$$${query_ip}MOVE X0"

        try:
            # 发送查询指令
            self.client_socket.send(command_str.encode('utf-8'))

            # 记录发送的指令
            self.command_sent.emit(query_ip, {
                "command": command_str,
                "timestamp": time.time()
            })

            logger.info(f"查询指令已发送: {command_str}")
            return True

        except Exception as e:
            logger.error(f"发送查询指令失败: {e}")
            self.error_occurred.emit(f"发送查询指令失败: {e}")
            return False

    def _send_init_command(self) -> None:
        """发送初始化命令"""
        init_data = {
            "command_type": "init",
            "client_info": "25AutoAssembly",
            "timestamp": time.time()
        }
        
        try:
            json_data = json.dumps(init_data, ensure_ascii=False)
            message = json_data.encode('utf-8')
            self.socket.sendall(len(message).to_bytes(4, byteorder='big'))
            self.socket.sendall(message)
            logger.info("初始化命令已发送")
            
        except Exception as e:
            logger.error(f"发送初始化命令失败: {e}")
    
    def _send_heartbeat(self) -> None:
        """发送心跳包"""
        if not self.connected:
            return
        
        heartbeat_data = {
            "command_type": "heartbeat",
            "timestamp": time.time()
        }
        
        try:
            if not self.client_socket:
                return

            json_data = json.dumps(heartbeat_data)
            message = json_data.encode('utf-8')
            self.client_socket.sendall(len(message).to_bytes(4, byteorder='big'))
            self.client_socket.sendall(message)
            logger.debug("心跳包已发送")
            
        except Exception as e:
            logger.error(f"发送心跳包失败: {e}")
            # 心跳已禁用，此方法不应被调用
            # 客户端连接断开检测由接收循环处理
    
    def _server_loop(self) -> None:
        """服务器循环，等待客户端连接"""
        logger.info("服务器线程已启动，等待客户端连接")

        while self.server_running:
            try:
                # 等待客户端连接
                logger.info("等待运动控制计算机连接...")
                client_socket, client_address = self.server_socket.accept()

                logger.info(f"客户端已连接: {client_address}")
                self.client_socket = client_socket
                self.connected = True

                # 发送连接状态变化信号
                self.connection_changed.emit(True)

                # 启动接收线程
                self.receive_thread = threading.Thread(target=self._receive_loop, daemon=True)
                self.receive_thread.start()

                # 心跳已禁用 - 客户端不需要心跳包
                # QMetaObject.invokeMethod(self.heartbeat_timer, "start", Qt.QueuedConnection)

                # 等待接收线程结束（客户端断开）
                if self.receive_thread:
                    self.receive_thread.join()

                # 客户端断开后，重置状态，准备下一次连接
                self.connected = False
                self.client_socket = None
                logger.info("准备接受新的客户端连接...")

            except Exception as e:
                if self.server_running:  # 只有在服务器运行时才记录错误
                    logger.error(f"服务器循环异常: {e}")
                break

        logger.info("服务器线程已结束")

    def _receive_loop(self) -> None:
        """接收数据循环 - 处理$Frame{JSON}$End格式"""
        logger.info("接收线程已启动")

        while self.running and self.connected:
            try:
                # 接收原始字节数据
                raw_data = self.client_socket.recv(1024)
                if not raw_data:
                    # 客户端断开连接
                    logger.info("客户端断开连接")
                    self.connected = False
                    self.connection_changed.emit(False)
                    break

                # 解码为UTF-8文本
                try:
                    data = raw_data.decode('utf-8', errors='ignore')
                    if data.strip():  # 只处理非空数据
                        print(f"\n=== 服务端接收数据 ===")
                        print(f"时间: {time.strftime('%H:%M:%S')}")
                        print(f"数据: {data}")
                        print(f"数据长度: {len(data)} 字符")
                        print("=" * 50)
                        logger.info(f"接收数据: {data}")
                except UnicodeDecodeError as e:
                    logger.error(f"数据解码失败: {e}")
                    continue

                if not data:
                    continue

                # 添加到缓冲区
                self.buffer += data

                # 处理缓冲区中的完整帧
                self._process_buffer()

            except socket.timeout:
                continue
            except ConnectionResetError:
                # 客户端强制断开连接
                logger.info("客户端强制断开连接")
                self.connected = False
                self.connection_changed.emit(False)
                break
            except Exception as e:
                if self.running:
                    logger.error(f"接收数据异常: {e}")
                    self.error_occurred.emit(f"接收数据异常: {e}")
                    self.connected = False
                    self.connection_changed.emit(False)
                break

        # 心跳已禁用，无需停止
        # if self.heartbeat_timer.isActive():
        #     QMetaObject.invokeMethod(self.heartbeat_timer, "stop", Qt.QueuedConnection)

        # 关闭客户端连接
        if self.client_socket:
            try:
                self.client_socket.close()
            except:
                pass
            self.client_socket = None

        logger.info("接收线程已结束")

    def _process_buffer(self) -> None:
        """处理接收缓冲区，解析完整帧"""
        while True:
            # 查找帧开始和结束标记
            start_pos = self.buffer.find('$Frame')
            if start_pos == -1:
                break

            end_pos = self.buffer.find('$End', start_pos)
            if end_pos == -1:
                break

            # 提取完整帧
            frame_start = start_pos + 6  # '$Frame'长度
            frame_data = self.buffer[frame_start:end_pos]

            # 解析JSON数据
            try:
                print(f"\n=== JSON解析 ===")
                print(f"提取的JSON数据: {frame_data}")

                pose_data = json.loads(frame_data)
                print(f"解析成功，设备数量: {len(pose_data.get('devices', []))}")

                self.pose_data_received.emit(pose_data)
                self._handle_received_data(pose_data)

            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
                print(f"原始数据: {frame_data}")
                logger.error(f"JSON解析失败: {e}")
                self.error_occurred.emit(f"JSON解析失败: {e}")

            # 移除已处理的数据
            self.buffer = self.buffer[end_pos + 4:]  # '$End'长度
    
    def _handle_received_data(self, data: Dict[str, Any]) -> None:
        """
        处理接收到的数据 - 只处理当前选择设备的数据

        Args:
            data: 接收到的数据
        """
        try:
            print(f"\n=== 数据处理 ===")
            # 处理新的位姿数据格式: {"devices": [{"id": "device1", "X": 0, "Y": 0, ...}]}
            devices = data.get('devices', [])
            print(f"接收到 {len(devices)} 个设备的数据")

            # 检查是否是查询结果 - 提取所有设备的M1字段
            m1_values = []
            for device in devices:
                m1_value = device.get('M1', None)
                if m1_value is not None:
                    m1_values.append(m1_value)

            # 如果有M1字段，发送查询结果信号
            if m1_values:
                self.query_result_received.emit(m1_values)
                logger.info(f"查询结果 - M1字段: {m1_values}")
                print(f"查询结果 - M1字段: {m1_values}")

            # 获取当前选择设备的IP
            current_device_ip = self.current_device.ip_address
            print(f"当前选择设备: {self.current_device.display_name}")
            print(f"当前设备IP: {current_device_ip}")

            if not current_device_ip:
                print("错误: 当前设备IP为空")
                return

            # 只处理当前选择设备的数据
            found_current_device = False
            for device in devices:
                device_id = device.get('id', '')
                print(f"处理设备: {device_id}")

                # 根据device_id映射到IP地址
                device_ip = self._get_device_ip_by_id(device_id)
                print(f"设备 {device_id} 映射到IP: {device_ip}")

                # 只处理当前选择的设备
                if device_ip == current_device_ip:
                    found_current_device = True
                    print(f"✅ 找到当前设备数据: {device_id} -> {device_ip}")
                    # 确保设备状态存在
                    if device_ip not in self.device_statuses:
                        self.device_statuses[device_ip] = DeviceStatus(
                            device_ip=device_ip,
                            connected=True,
                            position={"x": 0.0, "y": 0.0, "z": 0.0, "rx": 0.0, "ry": 0.0, "rz": 0.0},
                            last_update=0.0
                        )

                    status = self.device_statuses[device_ip]
                    status.connected = True
                    # 根据新的字段映射关系解析数据
                    # JSON字段 → GUI显示映射：
                    # B → X, C → Y(取反), A → Z(取反)
                    # Y → A, X → B(取反), Z → C(取反)
                    status.position = {
                        "x": device.get('B', 0),           # JSON的B → GUI的X
                        "y": -device.get('C', 0),          # JSON的C → GUI的Y (取反)
                        "z": -device.get('A', 0),          # JSON的A → GUI的Z (取反)
                        "rx": device.get('Y', 0),          # JSON的Y → GUI的A
                        "ry": -device.get('X', 0),         # JSON的X → GUI的B (取反)
                        "rz": -device.get('Z', 0)          # JSON的Z → GUI的C (取反)
                    }
                    status.last_update = time.time()

                    print(f"📊 原始JSON数据:")
                    print(f"   JSON_X: {device.get('X', 0)}")
                    print(f"   JSON_Y: {device.get('Y', 0)}")
                    print(f"   JSON_Z: {device.get('Z', 0)}")
                    print(f"   JSON_A: {device.get('A', 0)}")
                    print(f"   JSON_B: {device.get('B', 0)}")
                    print(f"   JSON_C: {device.get('C', 0)}")
                    print(f"   M1: {device.get('M1', 0)}")

                    print(f"📊 映射后GUI显示数据:")
                    print(f"   GUI_X: {status.position['x']} (来自JSON_B)")
                    print(f"   GUI_Y: {status.position['y']} (来自JSON_C, 取反)")
                    print(f"   GUI_Z: {status.position['z']} (来自JSON_A, 取反)")
                    print(f"   GUI_A: {status.position['rx']} (来自JSON_Y)")
                    print(f"   GUI_B: {status.position['ry']} (来自JSON_X, 取反)")
                    print(f"   GUI_C: {status.position['rz']} (来自JSON_Z, 取反)")

                    # 发送设备状态更新信号
                    signal_data = {
                        "position": status.position,
                        "connected": True,
                        "M1": device.get('M1', 0)
                    }
                    print(f"🔔 发送界面更新信号: {signal_data}")
                    self.device_status_updated.emit(device_ip, signal_data)

                    logger.debug(f"当前设备位姿更新: {device_id} ({device_ip}) - 映射后GUI显示: X:{status.position['x']}, Y:{status.position['y']}, Z:{status.position['z']}, A:{status.position['rx']}, B:{status.position['ry']}, C:{status.position['rz']}")
                    break  # 找到当前设备后退出循环

            if not found_current_device:
                print(f"⚠️ 未找到当前设备 {current_device_ip} 的数据")

        except Exception as e:
            logger.error(f"处理位姿数据异常: {e}")
            self.error_occurred.emit(f"处理位姿数据异常: {e}")

    def _get_device_ip_by_id(self, device_id: str) -> Optional[str]:
        """根据设备ID获取设备IP（从配置文件读取）"""
        return self.device_config.get_device_ip(device_id)

    def _get_device_id_by_ip(self, device_ip: str) -> Optional[str]:
        """根据设备IP获取设备ID（从配置文件读取）"""
        return self.device_config.get_device_id_by_ip(device_ip)

    def get_device_status(self, device_ip: str) -> Optional[DeviceStatus]:
        """
        获取设备状态
        
        Args:
            device_ip: 设备IP地址
            
        Returns:
            设备状态
        """
        return self.device_statuses.get(device_ip)
    
    def get_all_device_statuses(self) -> Dict[str, DeviceStatus]:
        """获取所有设备状态"""
        return self.device_statuses.copy()
    
    def is_connected(self) -> bool:
        """检查是否连接"""
        return self.connected
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop_server()
        return False
