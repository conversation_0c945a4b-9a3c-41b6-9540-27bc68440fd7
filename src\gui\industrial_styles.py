#!/usr/bin/env python3
"""
25AutoAssembly 工业级界面样式
专业的工业软件UI样式定义
"""

# 工业级配色方案
INDUSTRIAL_COLORS = {
    # 主色调 - 深蓝灰色系
    'primary_dark': '#1e2329',      # 主背景色
    'primary_medium': '#2c3e50',    # 次要背景色
    'primary_light': '#34495e',     # 浅背景色
    
    # 强调色 - 蓝色系
    'accent_blue': '#3498db',       # 主要强调色
    'accent_blue_hover': '#2980b9', # 悬停状态
    'accent_blue_pressed': '#21618c', # 按下状态
    
    # 状态色
    'success': '#27ae60',           # 成功/连接
    'warning': '#f39c12',           # 警告
    'danger': '#e74c3c',            # 错误/断开
    'info': '#3498db',              # 信息
    
    # 文本色
    'text_primary': '#ecf0f1',      # 主要文本
    'text_secondary': '#bdc3c7',    # 次要文本
    'text_disabled': '#7f8c8d',     # 禁用文本
    
    # 边框色
    'border_light': '#4a5568',      # 浅边框
    'border_medium': '#2d3748',     # 中等边框
    'border_dark': '#1a202c',       # 深边框
    
    # 数据显示色
    'data_value': '#00d4aa',        # 数据值颜色
    'data_label': '#a0aec0',        # 数据标签颜色
}

def get_main_window_style():
    """获取主窗口样式"""
    return f"""
    QMainWindow {{
        background-color: {INDUSTRIAL_COLORS['primary_dark']};
        color: {INDUSTRIAL_COLORS['text_primary']};
    }}
    
    QWidget {{
        background-color: {INDUSTRIAL_COLORS['primary_dark']};
        color: {INDUSTRIAL_COLORS['text_primary']};
        font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif;
    }}
    """

def get_groupbox_style():
    """获取分组框样式"""
    return f"""
    QGroupBox {{
        font-size: 14px;
        font-weight: bold;
        color: {INDUSTRIAL_COLORS['text_primary']};
        border: 2px solid {INDUSTRIAL_COLORS['border_medium']};
        border-radius: 8px;
        margin-top: 12px;
        padding-top: 8px;
        background-color: {INDUSTRIAL_COLORS['primary_medium']};
    }}
    
    QGroupBox::title {{
        subcontrol-origin: margin;
        left: 15px;
        padding: 0 8px 0 8px;
        color: {INDUSTRIAL_COLORS['accent_blue']};
        background-color: {INDUSTRIAL_COLORS['primary_medium']};
    }}
    """

def get_button_style():
    """获取按钮样式"""
    return f"""
    QPushButton {{
        background-color: {INDUSTRIAL_COLORS['accent_blue']};
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: bold;
        font-size: 12px;
        min-height: 24px;
        min-width: 80px;
    }}

    QPushButton:hover {{
        background-color: {INDUSTRIAL_COLORS['accent_blue_hover']};
        transform: translateY(-1px);
    }}

    QPushButton:pressed {{
        background-color: {INDUSTRIAL_COLORS['accent_blue_pressed']};
        transform: translateY(0px);
    }}

    QPushButton:disabled {{
        background-color: {INDUSTRIAL_COLORS['border_light']};
        color: {INDUSTRIAL_COLORS['text_disabled']};
    }}

    /* 成功按钮样式 */
    QPushButton[buttonType="success"] {{
        background-color: {INDUSTRIAL_COLORS['success']};
    }}

    QPushButton[buttonType="success"]:hover {{
        background-color: #229954;
    }}

    /* 危险按钮样式 */
    QPushButton[buttonType="danger"] {{
        background-color: {INDUSTRIAL_COLORS['danger']};
    }}

    QPushButton[buttonType="danger"]:hover {{
        background-color: #c0392b;
    }}
    """

def get_input_style():
    """获取输入框样式"""
    return f"""
    QLineEdit, QDoubleSpinBox, QSpinBox {{
        background-color: {INDUSTRIAL_COLORS['primary_light']};
        border: 2px solid {INDUSTRIAL_COLORS['border_light']};
        border-radius: 4px;
        padding: 6px 10px;
        font-size: 12px;
        color: {INDUSTRIAL_COLORS['text_primary']};
        min-height: 20px;
    }}

    QLineEdit:focus, QDoubleSpinBox:focus, QSpinBox:focus {{
        border-color: {INDUSTRIAL_COLORS['accent_blue']};
        background-color: {INDUSTRIAL_COLORS['primary_medium']};
    }}

    QLineEdit:read-only {{
        background-color: {INDUSTRIAL_COLORS['primary_medium']};
        color: {INDUSTRIAL_COLORS['text_secondary']};
        border-color: {INDUSTRIAL_COLORS['border_medium']};
    }}

    /* SpinBox 按钮样式 */
    QSpinBox::up-button, QDoubleSpinBox::up-button {{
        background-color: {INDUSTRIAL_COLORS['border_light']};
        border: none;
        border-radius: 2px;
        width: 16px;
    }}

    QSpinBox::down-button, QDoubleSpinBox::down-button {{
        background-color: {INDUSTRIAL_COLORS['border_light']};
        border: none;
        border-radius: 2px;
        width: 16px;
    }}
    """

def get_label_style():
    """获取标签样式"""
    return f"""
    QLabel {{
        color: {INDUSTRIAL_COLORS['text_primary']};
        font-size: 12px;
    }}

    /* 状态标签样式 */
    QLabel[labelType="status"] {{
        font-weight: bold;
        font-size: 13px;
        padding: 6px 12px;
        border-radius: 4px;
    }}

    QLabel[labelType="status"][status="connected"] {{
        color: {INDUSTRIAL_COLORS['success']};
        background-color: rgba(39, 174, 96, 0.15);
        border: 1px solid {INDUSTRIAL_COLORS['success']};
    }}

    QLabel[labelType="status"][status="disconnected"] {{
        color: {INDUSTRIAL_COLORS['danger']};
        background-color: rgba(231, 76, 60, 0.15);
        border: 1px solid {INDUSTRIAL_COLORS['danger']};
    }}

    QLabel[labelType="status"][status="warning"] {{
        color: {INDUSTRIAL_COLORS['warning']};
        background-color: rgba(243, 156, 18, 0.15);
        border: 1px solid {INDUSTRIAL_COLORS['warning']};
    }}

    /* 数据值标签 */
    QLabel[labelType="data_value"] {{
        font-size: 14px;
        font-weight: bold;
        color: {INDUSTRIAL_COLORS['data_value']};
        background-color: {INDUSTRIAL_COLORS['primary_light']};
        padding: 8px 12px;
        border-radius: 4px;
        border: 1px solid {INDUSTRIAL_COLORS['border_light']};
        min-width: 80px;
    }}

    /* 数据标签 */
    QLabel[labelType="data_label"] {{
        font-weight: bold;
        font-size: 12px;
        color: {INDUSTRIAL_COLORS['data_label']};
    }}
    """

def get_radio_button_style():
    """获取单选按钮样式"""
    return f"""
    QRadioButton {{
        color: {INDUSTRIAL_COLORS['text_primary']};
        font-size: 13px;
        spacing: 8px;
    }}
    
    QRadioButton::indicator {{
        width: 16px;
        height: 16px;
        border-radius: 8px;
        border: 2px solid {INDUSTRIAL_COLORS['border_light']};
        background-color: {INDUSTRIAL_COLORS['primary_light']};
    }}
    
    QRadioButton::indicator:checked {{
        background-color: {INDUSTRIAL_COLORS['accent_blue']};
        border-color: {INDUSTRIAL_COLORS['accent_blue']};
    }}
    
    QRadioButton::indicator:hover {{
        border-color: {INDUSTRIAL_COLORS['accent_blue_hover']};
    }}
    """

def get_text_edit_style():
    """获取文本编辑器样式"""
    return f"""
    QTextEdit {{
        background-color: {INDUSTRIAL_COLORS['primary_light']};
        border: 2px solid {INDUSTRIAL_COLORS['border_light']};
        border-radius: 6px;
        padding: 12px;
        font-family: "Consolas", "Monaco", "Microsoft YaHei", monospace;
        font-size: 13px;
        line-height: 1.4;
        color: {INDUSTRIAL_COLORS['text_primary']};
        selection-background-color: {INDUSTRIAL_COLORS['accent_blue']};
        min-height: 200px;
    }}

    QTextEdit:focus {{
        border-color: {INDUSTRIAL_COLORS['accent_blue']};
    }}

    /* 滚动条样式 */
    QTextEdit QScrollBar:vertical {{
        background-color: {INDUSTRIAL_COLORS['primary_medium']};
        width: 12px;
        border-radius: 6px;
    }}

    QTextEdit QScrollBar::handle:vertical {{
        background-color: {INDUSTRIAL_COLORS['border_light']};
        border-radius: 6px;
        min-height: 20px;
    }}

    QTextEdit QScrollBar::handle:vertical:hover {{
        background-color: {INDUSTRIAL_COLORS['accent_blue']};
    }}
    """

def get_complete_style():
    """获取完整的样式表"""
    return (
        get_main_window_style() +
        get_groupbox_style() +
        get_button_style() +
        get_input_style() +
        get_label_style() +
        get_radio_button_style() +
        get_text_edit_style()
    )
