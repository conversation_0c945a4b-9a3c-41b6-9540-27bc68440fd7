# 25AutoAssembly Build Script
# PyInstaller packaging script for optimized src structure

param(
    [switch]$Clean,
    [switch]$Debug,
    [switch]$Test
)

# Error handling
$ErrorActionPreference = "Stop"

# Get script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $ScriptDir

Write-Host "Starting 25AutoAssembly build process..." -ForegroundColor Green
Write-Host "Project directory: $ScriptDir" -ForegroundColor Cyan

# Clean old build files
if ($Clean) {
    Write-Host "Cleaning old build files..." -ForegroundColor Yellow
    if (Test-Path "build") { Remove-Item -Recurse -Force "build" }
    if (Test-Path "dist") { Remove-Item -Recurse -Force "dist" }
    Write-Host "Cleanup completed" -ForegroundColor Green
}

# Verify required files
Write-Host "Verifying project files..." -ForegroundColor Yellow
$requiredFiles = @("main.py", "main.spec")
$requiredDirs = @("src", "logs")

foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) {
        Write-Host "Missing required file: $file" -ForegroundColor Red
        exit 1
    }
}

foreach ($dir in $requiredDirs) {
    if (-not (Test-Path $dir)) {
        Write-Host "Missing required directory: $dir" -ForegroundColor Red
        exit 1
    }
}

# Verify core modules in src directory
$srcModules = @("src\gui", "src\config", "src\data_models", "src\device_communication", "src\utils")
foreach ($module in $srcModules) {
    if (-not (Test-Path $module)) {
        Write-Host "Missing core module: $module" -ForegroundColor Red
        exit 1
    }
}

Write-Host "Project file verification passed" -ForegroundColor Green

# Check PyInstaller
Write-Host "Checking PyInstaller..." -ForegroundColor Yellow
try {
    $pyinstallerVersion = python -c "import PyInstaller; print(PyInstaller.__version__)" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "PyInstaller version: $pyinstallerVersion" -ForegroundColor Green
    } else {
        Write-Host "PyInstaller not installed, installing..." -ForegroundColor Yellow
        pip install pyinstaller
        if ($LASTEXITCODE -ne 0) {
            Write-Host "PyInstaller installation failed" -ForegroundColor Red
            exit 1
        }
        Write-Host "PyInstaller installation successful" -ForegroundColor Green
    }
} catch {
    Write-Host "Error checking PyInstaller: $_" -ForegroundColor Red
    exit 1
}

# Start packaging
Write-Host "Starting PyInstaller packaging..." -ForegroundColor Yellow
$buildArgs = @("--clean", "--noconfirm")
if ($Debug) {
    $buildArgs += "--debug=all"
    Write-Host "Debug mode enabled" -ForegroundColor Cyan
}

try {
    & pyinstaller @buildArgs main.spec
    if ($LASTEXITCODE -ne 0) {
        Write-Host "PyInstaller packaging failed" -ForegroundColor Red
        exit 1
    }
    Write-Host "PyInstaller packaging successful" -ForegroundColor Green
} catch {
    Write-Host "Error during packaging: $_" -ForegroundColor Red
    exit 1
}

# Verify packaging results
Write-Host "Verifying packaging results..." -ForegroundColor Yellow
$exePath = "dist\25AutoAssembly\25AutoAssembly.exe"
if (Test-Path $exePath) {
    $fileSize = (Get-Item $exePath).Length / 1MB
    Write-Host "Executable file generated: $exePath" -ForegroundColor Green
    Write-Host "File size: $([math]::Round($fileSize, 2)) MB" -ForegroundColor Cyan
} else {
    Write-Host "Executable file not generated" -ForegroundColor Red
    exit 1
}

# Test run
if ($Test) {
    Write-Host "Testing executable file..." -ForegroundColor Yellow
    try {
        Start-Process -FilePath $exePath -WindowStyle Normal
        Write-Host "Program startup test successful" -ForegroundColor Green
        Write-Host "Please manually verify program functionality" -ForegroundColor Yellow
    } catch {
        Write-Host "Program startup test failed: $_" -ForegroundColor Red
    }
}

Write-Host "Packaging completed!" -ForegroundColor Green
Write-Host "Output directory: dist\25AutoAssembly\" -ForegroundColor Cyan
Write-Host "Executable file: $exePath" -ForegroundColor Cyan

# Usage instructions
Write-Host "`nUsage instructions:" -ForegroundColor Yellow
Write-Host "   - Direct run: .\dist\25AutoAssembly\25AutoAssembly.exe" -ForegroundColor White
Write-Host "   - Clean rebuild: .\build_main.ps1 -Clean" -ForegroundColor White
Write-Host "   - Debug mode: .\build_main.ps1 -Debug" -ForegroundColor White
Write-Host "   - Build and test: .\build_main.ps1 -Test" -ForegroundColor White
