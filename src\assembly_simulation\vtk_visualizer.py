#!/usr/bin/env python3
"""
25AutoAssembly VTK可视化器模块

基于VTK实现URDF模型的3D可视化，严格参考testsimple/urdf_viewer_standalone.py中的逻辑
包括：
- VTK渲染器设置和管理
- 几何体创建和变换
- 坐标系显示和相机控制
- Qt集成和交互处理
"""

import os
import logging
from typing import Dict, List, Optional, Tuple, Any
import numpy as np

# VTK相关导入
VTK_AVAILABLE = False
QVTK_AVAILABLE = False
try:
    import vtk
    print(f"VTK version: {vtk.vtkVersion.GetVTKVersion()}")
    VTK_AVAILABLE = True
    
    # 尝试导入Qt集成组件
    try:
        from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
        QVTK_AVAILABLE = True
        print("VTK-Qt integration available")
    except ImportError as e:
        print(f"VTK-Qt integration not available: {e}")
                
except ImportError as e:
    print(f"VTK not available: {e}")
    VTK_AVAILABLE = False

# Qt相关导入
try:
    from PyQt5.QtWidgets import QWidget, QVBoxLayout, QApplication
    from PyQt5.QtCore import Qt
    QT_AVAILABLE = True
except ImportError as e:
    logging.warning(f"PyQt5导入失败: {e}")
    QT_AVAILABLE = False

# 获取日志记录器
logger = logging.getLogger(__name__)


class VTKVisualizer:
    """
    VTK可视化器 - 基于standalone版本的VTK逻辑
    
    负责URDF模型的3D可视化，包括几何体创建、变换应用和渲染管理
    """
    
    def __init__(self, parent_widget=None):
        """
        初始化VTK可视化器
        
        Args:
            parent_widget: 父Qt组件
        """
        self.parent_widget = parent_widget
        self.vtk_widget = None
        self.renderer = None
        self.vtk_actors = []  # 存储所有VTK actors
        self.link_actors = {}  # 存储每个link对应的actors，用于姿态更新
        self.urdf_parser = None
        
        logger.info("VTKVisualizer初始化完成")
    
    def initialize_vtk_widget(self) -> bool:
        """
        初始化VTK组件
        
        Returns:
            bool: 初始化成功返回True，失败返回False
        """
        if not (VTK_AVAILABLE and QVTK_AVAILABLE and QT_AVAILABLE):
            logger.error("VTK或Qt组件不可用")
            return False
        
        try:
            # 创建VTK渲染组件
            self.vtk_widget = QVTKRenderWindowInteractor(self.parent_widget)
            
            # 设置VTK渲染器
            success = self._setup_vtk_renderer()
            if not success:
                logger.error("VTK渲染器设置失败")
                return False
            
            logger.info("VTK组件初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"VTK组件初始化失败: {e}")
            return False
    
    def _setup_vtk_renderer(self) -> bool:
        """
        设置VTK渲染器 - 基于standalone版本
        
        Returns:
            bool: 设置成功返回True，失败返回False
        """
        try:
            # 创建渲染器
            self.renderer = vtk.vtkRenderer()
            self.renderer.SetBackground(0.2, 0.3, 0.4)  # 深蓝灰色背景
            
            # 添加到渲染窗口
            self.vtk_widget.GetRenderWindow().AddRenderer(self.renderer)
            
            # 添加坐标轴
            axes = vtk.vtkAxesActor()
            axes.SetTotalLength(0.3, 0.3, 0.3)
            self.renderer.AddActor(axes)
            
            # 添加网格
            self._add_grid()
            
            logger.info("VTK渲染器设置完成")
            return True
            
        except Exception as e:
            logger.error(f"VTK渲染器设置失败: {e}")
            return False
    
    def _add_grid(self):
        """添加网格 - 基于standalone版本"""
        try:
            # 创建网格平面
            plane = vtk.vtkPlaneSource()
            plane.SetXResolution(10)
            plane.SetYResolution(10)
            plane.SetOrigin(-1, -1, 0)
            plane.SetPoint1(1, -1, 0)
            plane.SetPoint2(-1, 1, 0)
            
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputConnection(plane.GetOutputPort())
            
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            actor.GetProperty().SetRepresentationToWireframe()
            actor.GetProperty().SetColor(0.5, 0.5, 0.5)
            actor.GetProperty().SetOpacity(0.3)
            
            self.renderer.AddActor(actor)
            
        except Exception as e:
            logger.error(f"添加网格失败: {e}")
    
    def get_vtk_widget(self):
        """
        获取VTK组件
        
        Returns:
            QVTKRenderWindowInteractor: VTK组件
        """
        return self.vtk_widget
    
    def load_urdf_model(self, urdf_parser) -> bool:
        """
        加载URDF模型进行可视化 - 基于standalone版本的visualize_urdf逻辑
        
        Args:
            urdf_parser: URDF解析器实例
            
        Returns:
            bool: 加载成功返回True，失败返回False
        """
        if not (VTK_AVAILABLE and QVTK_AVAILABLE):
            logger.error("VTK组件不可用")
            return False
        
        if not urdf_parser.is_loaded():
            logger.error("URDF解析器未加载模型")
            return False
        
        try:
            self.urdf_parser = urdf_parser
            
            # 清除之前的actors
            self._clear_previous_actors()
            
            # 可视化URDF
            success = self._visualize_urdf()
            
            if success:
                # 重置相机视角
                self.reset_camera()
                logger.info("URDF模型加载成功")
                return True
            else:
                logger.error("URDF模型可视化失败")
                return False
                
        except Exception as e:
            logger.error(f"加载URDF模型失败: {e}")
            return False
    
    def _clear_previous_actors(self):
        """清除之前的actors"""
        for actor in self.vtk_actors:
            self.renderer.RemoveActor(actor)
        self.vtk_actors.clear()
        self.link_actors.clear()
    
    def _visualize_urdf(self) -> bool:
        """
        可视化URDF - 基于standalone版本的visualize_urdf逻辑
        
        Returns:
            bool: 可视化成功返回True，失败返回False
        """
        try:
            colors = [(0.8, 0.2, 0.2), (0.2, 0.8, 0.2), (0.2, 0.2, 0.8),
                     (0.8, 0.8, 0.2), (0.8, 0.2, 0.8), (0.2, 0.8, 0.8)]

            links = self.urdf_parser.get_links()
            logger.info(f"开始可视化 {len(links)} 个连杆...")

            for i, link in enumerate(links):
                logger.info(f"处理连杆 {i}: {link['name']} 包含 {len(link['visual'])} 个visual元素")

                # 获取从根链接到当前链接的累积变换
                transform_chain = self.urdf_parser.get_transform_chain(link['name'])
                cumulative_transform = self.urdf_parser.compute_cumulative_transform(transform_chain)

                logger.info(f"  连杆 {link['name']} 的变换链: {len(transform_chain)} 个变换")

                # 初始化该连杆的actor列表
                link_name = link['name']
                if link_name not in self.link_actors:
                    self.link_actors[link_name] = []

                for j, visual in enumerate(link['visual']):
                    logger.info(f"  Visual元素 {j}: {visual}")

                    if visual['geometry']:
                        logger.info(f"  为几何体创建actor: {visual['geometry']}")
                        actor = self._create_geometry_actor(visual['geometry'])
                        if actor:
                            # 设置颜色
                            color = colors[i % len(colors)]
                            actor.GetProperty().SetColor(*color)

                            # 计算visual变换矩阵
                            # visual['origin']是一个列表: [x, y, z, roll, pitch, yaw]
                            origin = visual.get('origin', [0, 0, 0, 0, 0, 0])
                            xyz = origin[:3]  # 前3个元素是位置
                            rpy = origin[3:]  # 后3个元素是旋转
                            visual_transform = self.urdf_parser.create_transform_matrix(xyz, rpy)

                            # 应用累积变换
                            success = self._apply_transform_to_actor(actor, visual, cumulative_transform)
                            if success:
                                self.renderer.AddActor(actor)
                                self.vtk_actors.append(actor)

                                # 存储到link_actors中，包含actor和visual变换信息
                                actor_info = {
                                    'actor': actor,
                                    'visual_transform': visual_transform
                                }
                                self.link_actors[link_name].append(actor_info)

                                logger.info(f"  成功添加 {link['name']} 的actor")
                            else:
                                logger.warning(f"  应用变换失败: {link['name']}")
                        else:
                            logger.warning(f"  创建actor失败: {link['name']}")
                    else:
                        logger.info(f"  Visual元素 {j} 没有几何体")

            logger.info(f"可视化完成，共创建 {len(self.vtk_actors)} 个组件")
            return True

        except Exception as e:
            logger.error(f"可视化过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _create_geometry_actor(self, geometry):
        """
        根据几何体类型创建VTK actor - 基于standalone版本

        Args:
            geometry: 几何体信息字典

        Returns:
            vtk.vtkActor: VTK actor对象，失败返回None
        """
        try:
            if geometry['type'] == 'box':
                return self._create_box_actor(geometry['size'])
            elif geometry['type'] == 'cylinder':
                return self._create_cylinder_actor(geometry['radius'], geometry['length'])
            elif geometry['type'] == 'sphere':
                return self._create_sphere_actor(geometry['radius'])
            elif geometry['type'] == 'mesh':
                return self._create_mesh_actor(geometry['filename'])
            else:
                logger.warning(f"未知的几何体类型: {geometry['type']}")
                return None
        except Exception as e:
            logger.error(f"创建几何体actor失败: {e}")
            return None

    def _create_box_actor(self, size):
        """创建立方体actor - 基于standalone版本"""
        box = vtk.vtkCubeSource()
        box.SetXLength(size[0])
        box.SetYLength(size[1])
        box.SetZLength(size[2])

        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputConnection(box.GetOutputPort())

        actor = vtk.vtkActor()
        actor.SetMapper(mapper)

        return actor

    def _create_cylinder_actor(self, radius, length):
        """创建圆柱体actor - 基于standalone版本"""
        cylinder = vtk.vtkCylinderSource()
        cylinder.SetRadius(radius)
        cylinder.SetHeight(length)
        cylinder.SetResolution(20)

        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputConnection(cylinder.GetOutputPort())

        actor = vtk.vtkActor()
        actor.SetMapper(mapper)

        return actor

    def _create_sphere_actor(self, radius):
        """创建球体actor - 基于standalone版本"""
        sphere = vtk.vtkSphereSource()
        sphere.SetRadius(radius)
        sphere.SetThetaResolution(20)
        sphere.SetPhiResolution(20)

        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputConnection(sphere.GetOutputPort())

        actor = vtk.vtkActor()
        actor.SetMapper(mapper)

        return actor

    def _create_mesh_actor(self, filename):
        """创建mesh actor - 基于standalone版本"""
        try:
            logger.info(f"尝试加载mesh: {filename}")

            # 检查文件是否存在
            if not os.path.exists(filename):
                logger.warning(f"Mesh文件不存在: {filename}")
                return None

            if filename.lower().endswith('.stl'):
                reader = vtk.vtkSTLReader()
                reader.SetFileName(filename)
                reader.Update()
                logger.info(f"成功加载STL: {filename}")
            elif filename.lower().endswith('.obj'):
                reader = vtk.vtkOBJReader()
                reader.SetFileName(filename)
                reader.Update()
                logger.info(f"成功加载OBJ: {filename}")
            else:
                logger.warning(f"不支持的mesh格式: {filename}")
                return None

            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputConnection(reader.GetOutputPort())

            actor = vtk.vtkActor()
            actor.SetMapper(mapper)

            return actor
        except Exception as e:
            logger.error(f"加载mesh失败 {filename}: {e}")
            return None

    def _apply_transform_to_actor(self, actor, visual, cumulative_transform) -> bool:
        """
        应用变换到actor - 基于standalone版本的变换逻辑

        Args:
            actor: VTK actor对象
            visual: visual信息字典
            cumulative_transform: 累积变换矩阵

        Returns:
            bool: 应用成功返回True，失败返回False
        """
        try:
            # 创建变换对象
            transform = vtk.vtkTransform()

            # 首先应用visual元素自身的origin变换
            visual_origin = visual['origin']
            if isinstance(visual_origin, dict):
                xyz = visual_origin['xyz']
                rpy = visual_origin['rpy']
            else:
                # 兼容旧格式
                xyz = visual_origin[:3]
                rpy = visual_origin[3:6]

            # 创建visual的变换矩阵
            visual_transform = self.urdf_parser.create_transform_matrix(xyz, rpy)

            # 组合累积变换和visual变换
            final_transform = np.dot(cumulative_transform, visual_transform)

            # 从变换矩阵中提取位置
            translation = final_transform[:3, 3]

            # 设置位置
            transform.Translate(translation[0], translation[1], translation[2])

            # 从旋转矩阵计算欧拉角并应用
            # 使用VTK的矩阵设置方法
            vtk_matrix = vtk.vtkMatrix4x4()
            for row in range(4):
                for col in range(4):
                    vtk_matrix.SetElement(row, col, final_transform[row, col])

            transform.SetMatrix(vtk_matrix)
            actor.SetUserTransform(transform)

            logger.debug(f"成功应用变换，位置: {translation}")
            return True

        except Exception as e:
            logger.error(f"应用变换失败: {e}")
            return False

    def reset_camera(self):
        """重置相机 - 基于standalone版本"""
        if VTK_AVAILABLE and QVTK_AVAILABLE and hasattr(self, 'renderer') and self.renderer:
            self.renderer.ResetCamera()
            if self.vtk_widget:
                self.vtk_widget.GetRenderWindow().Render()
            logger.info("相机视角已重置")

    def render(self):
        """渲染场景"""
        if self.vtk_widget:
            self.vtk_widget.GetRenderWindow().Render()

    def update_robot_pose(self, joint_values: Dict[str, float]) -> bool:
        """
        根据关节角度更新机器人姿态 - 基于standalone版本的update_robot_pose逻辑

        Args:
            joint_values: 关节名称到角度值的映射

        Returns:
            bool: 更新成功返回True，失败返回False
        """
        if not (VTK_AVAILABLE and QVTK_AVAILABLE and hasattr(self, 'renderer')):
            logger.warning("VTK组件不可用，无法更新机器人姿态")
            return False

        if not self.urdf_parser:
            logger.warning("URDF解析器不可用，无法更新机器人姿态")
            return False

        try:
            # 重新计算所有link的变换并更新actor位置
            # 严格基于testsimple/urdf_viewer_standalone.py中的update_robot_pose逻辑
            print(f"🔧 [VTK] 开始更新机器人姿态，关节值: {joint_values}")
            logger.info(f"更新机器人姿态，关节值: {joint_values}")

            if not self.link_actors:
                print("⚠️ [VTK] 警告：link_actors为空，无法更新姿态")
                logger.warning("link_actors为空，无法更新姿态")
                return False

            updated_count = 0
            for link_name, actors in self.link_actors.items():
                print(f"🔧 [VTK] 处理连杆: {link_name}, actors数量: {len(actors)}")

                # 获取变换链
                transform_chain = self.urdf_parser.get_transform_chain(link_name, joint_values)
                print(f"🔧 [VTK] 连杆 {link_name} 变换链长度: {len(transform_chain)}")

                cumulative_transform = self.urdf_parser.compute_cumulative_transform(transform_chain)
                print(f"🔧 [VTK] 连杆 {link_name} 累积变换位置: {cumulative_transform[:3, 3]}")

                # 更新每个actor的变换
                for i, actor_info in enumerate(actors):
                    actor = actor_info['actor']
                    visual_transform = actor_info['visual_transform']

                    # 组合累积变换和visual变换
                    final_transform = np.dot(cumulative_transform, visual_transform)

                    # 创建VTK变换
                    transform = vtk.vtkTransform()
                    vtk_matrix = vtk.vtkMatrix4x4()
                    for row in range(4):
                        for col in range(4):
                            vtk_matrix.SetElement(row, col, final_transform[row, col])

                    transform.SetMatrix(vtk_matrix)
                    actor.SetUserTransform(transform)

                    print(f"🔧 [VTK] 更新actor {i} 位置: {final_transform[:3, 3]}")
                    updated_count += 1

            print(f"🔧 [VTK] 共更新了 {updated_count} 个actor")

            # 刷新渲染
            print("🔧 [VTK] 刷新渲染...")
            self.render()

            print("✅ [VTK] 机器人姿态更新完成")
            logger.info("机器人姿态更新完成")
            return True

        except Exception as e:
            logger.error(f"更新机器人姿态失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def cleanup(self):
        """
        清理VTK资源 - 解决Windows平台退出时的OpenGL上下文错误
        """
        try:
            print("🧹 [VTK] 开始清理VTK资源...")

            # 清理actors
            if hasattr(self, 'vtk_actors') and self.vtk_actors:
                print(f"🧹 [VTK] 清理 {len(self.vtk_actors)} 个actors...")
                for actor in self.vtk_actors:
                    try:
                        if self.renderer:
                            self.renderer.RemoveActor(actor)
                    except Exception as e:
                        # 忽略清理过程中的错误
                        pass
                self.vtk_actors.clear()

            # 清理link_actors
            if hasattr(self, 'link_actors'):
                self.link_actors.clear()

            # 清理渲染器
            if hasattr(self, 'renderer') and self.renderer:
                print("🧹 [VTK] 清理渲染器...")
                try:
                    self.renderer.RemoveAllViewProps()
                    self.renderer = None
                except Exception as e:
                    # 忽略清理过程中的错误
                    pass

            # 清理VTK组件
            if hasattr(self, 'vtk_widget') and self.vtk_widget:
                print("🧹 [VTK] 清理VTK组件...")
                try:
                    # 先尝试正常关闭
                    render_window = self.vtk_widget.GetRenderWindow()
                    if render_window:
                        render_window.Finalize()

                    # 设置为None
                    self.vtk_widget = None
                except Exception as e:
                    # 忽略清理过程中的错误，这些通常是无害的
                    pass

            print("✅ [VTK] VTK资源清理完成")

        except Exception as e:
            # 清理过程中的错误通常是无害的，只记录但不抛出
            print(f"⚠️ [VTK] VTK清理过程中出现错误（通常无害）: {e}")

    def is_available(self) -> bool:
        """
        检查VTK组件是否可用

        Returns:
            bool: 可用返回True，否则返回False
        """
        return VTK_AVAILABLE and QVTK_AVAILABLE and QT_AVAILABLE
