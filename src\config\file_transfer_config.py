#!/usr/bin/env python3
"""
文件传输配置管理
用于保存和加载文件传输相关的用户配置
"""

import json
import os
from pathlib import Path


class FileTransferConfig:
    """文件传输配置管理类"""
    
    def __init__(self):
        self.config_dir = Path(__file__).parent
        self.config_file = self.config_dir / "file_transfer_settings.json"
        self.default_config = {
            "client": {
                "server_ip": "127.0.0.1",
                "server_port": "8888",
                "storage_path": ""
            },
            "server": {
                "server_ip": "127.0.0.1",
                "server_port": "8888",
                "file_path": ""
            }
        }
        
    def load_config(self):
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置，确保所有字段都存在
                    for section in self.default_config:
                        if section not in config:
                            config[section] = self.default_config[section]
                        else:
                            for key in self.default_config[section]:
                                if key not in config[section]:
                                    config[section][key] = self.default_config[section][key]
                    return config
            else:
                return self.default_config.copy()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self.default_config.copy()
            
    def save_config(self, config):
        """保存配置文件"""
        try:
            # 确保配置目录存在
            self.config_dir.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
            
    def get_client_config(self):
        """获取客户端配置"""
        config = self.load_config()
        return config.get("client", self.default_config["client"])
        
    def save_client_config(self, server_ip, server_port, storage_path):
        """保存客户端配置"""
        config = self.load_config()
        config["client"] = {
            "server_ip": server_ip,
            "server_port": server_port,
            "storage_path": storage_path
        }
        return self.save_config(config)
        
    def get_server_config(self):
        """获取服务器配置"""
        config = self.load_config()
        return config.get("server", self.default_config["server"])
        
    def save_server_config(self, server_ip, server_port, file_path):
        """保存服务器配置"""
        config = self.load_config()
        config["server"] = {
            "server_ip": server_ip,
            "server_port": server_port,
            "file_path": file_path
        }
        return self.save_config(config)


# 全局配置实例
file_transfer_config = FileTransferConfig()
