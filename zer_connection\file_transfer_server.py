#!/usr/bin/env python3
"""
TCP文件传输服务器
用于局域网文件传输的简单GUI服务器程序
"""

import sys
import os
import socket
import threading
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QLabel, QLineEdit, QPushButton,
                             QComboBox, QFileDialog, QTextEdit, QGroupBox,
                             QMessageBox, QProgressBar)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont
import json

# 添加项目根目录到路径，以便导入配置模块
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.file_transfer_config import file_transfer_config


class FileTransferServer(QMainWindow):
    """文件传输服务器主窗口"""
    
    def __init__(self):
        super().__init__()
        self.server_socket = None
        self.client_socket = None
        self.is_running = False
        self.selected_file_path = ""

        # 加载服务器配置
        self.server_config = file_transfer_config.get_server_config()

        self.init_ui()
        self.setup_style()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("TCP文件传输服务器")
        self.setMinimumSize(700, 650)  # 增加最小尺寸
        self.resize(800, 700)  # 增加默认尺寸
        
        # 居中显示
        self.center_window()
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(12)  # 减少间距
        main_layout.setContentsMargins(15, 15, 15, 15)  # 减少边距
        
        # 网络配置区域
        self.create_network_config(main_layout)
        
        # 文件选择区域
        self.create_file_selection(main_layout)
        
        # 传输控制区域
        self.create_transfer_control(main_layout)
        
        # 日志显示区域
        self.create_log_area(main_layout)
        
    def center_window(self):
        """窗口居中"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
        
    def create_network_config(self, parent_layout):
        """创建网络配置区域"""
        group = QGroupBox("网络配置")
        group.setMinimumHeight(120)  # 设置最小高度
        layout = QVBoxLayout(group)
        
        # IP配置
        ip_layout = QHBoxLayout()
        ip_layout.addWidget(QLabel("服务器IP:"))
        
        self.ip_combo = QComboBox()
        self.ip_combo.setEditable(True)
        self.ip_combo.setMinimumHeight(30)  # 设置下拉框最小高度
        self.ip_combo.addItems(self.get_local_ips())
        # 设置保存的IP值
        self.ip_combo.setCurrentText(self.server_config.get("server_ip", "127.0.0.1"))
        ip_layout.addWidget(self.ip_combo)

        layout.addLayout(ip_layout)

        # 端口配置
        port_layout = QHBoxLayout()
        port_layout.addWidget(QLabel("端口:"))

        self.port_edit = QLineEdit()
        self.port_edit.setMinimumHeight(30)  # 设置输入框最小高度
        # 设置保存的端口值
        self.port_edit.setText(self.server_config.get("server_port", "8888"))
        self.port_edit.setMaximumWidth(100)
        port_layout.addWidget(self.port_edit)
        port_layout.addStretch()
        
        layout.addLayout(port_layout)
        
        # 服务器控制按钮
        button_layout = QHBoxLayout()
        self.start_btn = QPushButton("启动服务器")
        self.start_btn.setMinimumHeight(35)  # 设置按钮最小高度
        self.start_btn.clicked.connect(self.start_server)

        self.stop_btn = QPushButton("停止服务器")
        self.stop_btn.setMinimumHeight(35)  # 设置按钮最小高度
        self.stop_btn.clicked.connect(self.stop_server)
        self.stop_btn.setEnabled(False)

        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.stop_btn)
        button_layout.addStretch()

        layout.addLayout(button_layout)
        
        parent_layout.addWidget(group)
        
    def create_file_selection(self, parent_layout):
        """创建文件选择区域"""
        group = QGroupBox("文件选择")
        group.setMinimumHeight(80)  # 设置最小高度
        layout = QVBoxLayout(group)
        
        # 文件路径显示
        path_layout = QHBoxLayout()
        path_layout.addWidget(QLabel("选择文件:"))
        
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setReadOnly(True)
        self.file_path_edit.setMinimumHeight(30)  # 设置输入框最小高度
        self.file_path_edit.setPlaceholderText("请选择要传输的文件...")
        # 设置保存的文件路径
        saved_file_path = self.server_config.get("file_path", "")
        if saved_file_path and os.path.exists(saved_file_path):
            self.file_path_edit.setText(saved_file_path)
            self.selected_file_path = saved_file_path
        path_layout.addWidget(self.file_path_edit)

        self.browse_btn = QPushButton("浏览...")
        self.browse_btn.setMinimumHeight(30)  # 设置按钮最小高度
        self.browse_btn.clicked.connect(self.browse_file)
        path_layout.addWidget(self.browse_btn)
        
        layout.addLayout(path_layout)
        
        parent_layout.addWidget(group)
        
    def create_transfer_control(self, parent_layout):
        """创建传输控制区域"""
        group = QGroupBox("传输控制")
        group.setMinimumHeight(80)  # 设置最小高度
        layout = QVBoxLayout(group)
        
        # 发送按钮
        button_layout = QHBoxLayout()
        self.send_btn = QPushButton("发送文件")
        self.send_btn.setMinimumHeight(35)  # 设置按钮最小高度
        self.send_btn.clicked.connect(self.send_file)
        self.send_btn.setEnabled(False)

        button_layout.addWidget(self.send_btn)
        button_layout.addStretch()

        layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        parent_layout.addWidget(group)
        
    def create_log_area(self, parent_layout):
        """创建日志显示区域"""
        group = QGroupBox("服务器日志")
        group.setMinimumHeight(250)  # 设置最小高度
        layout = QVBoxLayout(group)

        self.log_text = QTextEdit()
        self.log_text.setMinimumHeight(200)  # 设置日志文本框最小高度
        self.log_text.setMaximumHeight(400)  # 设置最大高度，避免过度拉伸
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)

        parent_layout.addWidget(group)
        
    def setup_style(self):
        """设置界面样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #4CAF50;
                border: none;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QLineEdit, QComboBox {
                padding: 5px;
                border: 1px solid #ddd;
                border-radius: 3px;
            }
        """)
        
    def get_local_ips(self):
        """获取本机IP地址列表"""
        ips = ["127.0.0.1"]
        try:
            import socket
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            if local_ip not in ips:
                ips.append(local_ip)
        except:
            pass
        return ips
        
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.append(f"[{self.get_timestamp()}] {message}")
        
    def get_timestamp(self):
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")

    def save_server_config(self):
        """保存服务器配置"""
        try:
            server_ip = self.ip_combo.currentText().strip()
            server_port = self.port_edit.text().strip()
            file_path = self.selected_file_path

            file_transfer_config.save_server_config(server_ip, server_port, file_path)
        except Exception as e:
            self.log_message(f"保存配置失败: {str(e)}")
        
    def browse_file(self):
        """浏览选择文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, 
            "选择要传输的文件", 
            "", 
            "文本文件 (*.txt);;所有文件 (*.*)"
        )
        
        if file_path:
            self.selected_file_path = file_path
            self.file_path_edit.setText(file_path)
            self.log_message(f"已选择文件: {os.path.basename(file_path)}")

            # 保存文件路径配置
            self.save_server_config()
            
    def start_server(self):
        """启动TCP服务器"""
        try:
            ip = self.ip_combo.currentText().strip()
            port = int(self.port_edit.text().strip())
            
            if not ip or port <= 0:
                QMessageBox.warning(self, "错误", "请输入有效的IP地址和端口号")
                return
                
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((ip, port))
            self.server_socket.listen(1)
            
            self.is_running = True
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.send_btn.setEnabled(True)
            
            self.log_message(f"服务器已启动: {ip}:{port}")
            self.log_message("等待客户端连接...")

            # 保存服务器配置
            self.save_server_config()

            # 启动监听线程
            self.listen_thread = threading.Thread(target=self.listen_for_clients, daemon=True)
            self.listen_thread.start()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动服务器失败: {str(e)}")
            self.log_message(f"启动失败: {str(e)}")
            
    def listen_for_clients(self):
        """监听客户端连接"""
        try:
            while self.is_running:
                if self.server_socket:
                    self.client_socket, addr = self.server_socket.accept()
                    self.log_message(f"客户端已连接: {addr[0]}:{addr[1]}")
        except Exception as e:
            if self.is_running:
                self.log_message(f"监听错误: {str(e)}")
                
    def send_file(self):
        """发送文件到客户端"""
        if not self.selected_file_path:
            QMessageBox.warning(self, "错误", "请先选择要发送的文件")
            return
            
        if not self.client_socket:
            QMessageBox.warning(self, "错误", "没有客户端连接")
            return
            
        try:
            # 读取文件内容
            with open(self.selected_file_path, 'r', encoding='utf-8') as f:
                file_content = f.read()
                
            # 准备文件信息
            file_info = {
                'filename': os.path.basename(self.selected_file_path),
                'size': len(file_content.encode('utf-8')),
                'content': file_content
            }
            
            # 发送文件信息（JSON格式）
            message = json.dumps(file_info, ensure_ascii=False)
            message_bytes = message.encode('utf-8')
            
            # 先发送消息长度，再发送消息内容
            length_bytes = len(message_bytes).to_bytes(4, byteorder='big')
            self.client_socket.send(length_bytes)
            self.client_socket.send(message_bytes)
            
            self.log_message(f"文件发送成功: {file_info['filename']} ({file_info['size']} 字节)")
            QMessageBox.information(self, "成功", "文件发送成功！")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"发送文件失败: {str(e)}")
            self.log_message(f"发送失败: {str(e)}")
            
    def stop_server(self):
        """停止TCP服务器"""
        try:
            self.is_running = False
            
            if self.client_socket:
                self.client_socket.close()
                self.client_socket = None
                
            if self.server_socket:
                self.server_socket.close()
                self.server_socket = None
                
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            self.send_btn.setEnabled(False)
            
            self.log_message("服务器已停止")
            
        except Exception as e:
            self.log_message(f"停止服务器错误: {str(e)}")
            
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.is_running:
            self.stop_server()
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # 设置应用程序字体
    font = QFont("Microsoft YaHei", 10)
    app.setFont(font)
    
    window = FileTransferServer()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
