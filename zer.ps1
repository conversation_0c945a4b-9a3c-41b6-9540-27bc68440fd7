# TCP文件传输服务器打包脚本
# 使用PyInstaller将file_transfer_server.py打包为独立的exe文件

Write-Host "开始打包TCP文件传输服务器..." -ForegroundColor Green

# 检查conda环境
Write-Host "检查conda环境..." -ForegroundColor Yellow
$condaEnv = conda info --envs | Select-String "25autoassembly"
if ($condaEnv) {
    Write-Host "找到conda环境: 25autoassembly" -ForegroundColor Green
} else {
    Write-Host "未找到conda环境: 25autoassembly" -ForegroundColor Red
    Write-Host "请先创建conda环境: conda create -n 25autoassembly python=3.8" -ForegroundColor Yellow
    exit 1
}

# 激活conda环境
Write-Host "激活conda环境..." -ForegroundColor Yellow
conda activate 25autoassembly

# 检查PyInstaller是否安装
Write-Host "检查PyInstaller..." -ForegroundColor Yellow
$pyinstallerCheck = python -c "import PyInstaller; print('PyInstaller installed')" 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "PyInstaller已安装" -ForegroundColor Green
} else {
    Write-Host "PyInstaller未安装，正在安装..." -ForegroundColor Yellow
    pip install pyinstaller
    if ($LASTEXITCODE -eq 0) {
        Write-Host "PyInstaller安装成功" -ForegroundColor Green
    } else {
        Write-Host "PyInstaller安装失败" -ForegroundColor Red
        exit 1
    }
}

# 检查PyQt5是否安装
Write-Host "检查PyQt5..." -ForegroundColor Yellow
$pyqt5Check = python -c "import PyQt5; print('PyQt5 installed')" 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "PyQt5已安装" -ForegroundColor Green
} else {
    Write-Host "PyQt5未安装，正在安装..." -ForegroundColor Yellow
    pip install PyQt5
    if ($LASTEXITCODE -eq 0) {
        Write-Host "PyQt5安装成功" -ForegroundColor Green
    } else {
        Write-Host "PyQt5安装失败" -ForegroundColor Red
        exit 1
    }
}

# 清理之前的构建文件
Write-Host "清理之前的构建文件..." -ForegroundColor Yellow
if (Test-Path "build") {
    Remove-Item -Recurse -Force "build"
    Write-Host "已清理build目录" -ForegroundColor Green
}
if (Test-Path "dist") {
    Remove-Item -Recurse -Force "dist"
    Write-Host "已清理dist目录" -ForegroundColor Green
}

# 开始打包
Write-Host "开始使用PyInstaller打包..." -ForegroundColor Yellow
Write-Host "使用spec文件: zer.spec" -ForegroundColor Cyan

pyinstaller zer.spec

# 检查打包结果
if ($LASTEXITCODE -eq 0) {
    Write-Host "打包成功！" -ForegroundColor Green

    # 检查exe文件是否存在
    $exePath = "dist\FileTransferServer.exe"
    if (Test-Path $exePath) {
        Write-Host "可执行文件已生成: $exePath" -ForegroundColor Green

        # 获取文件大小
        $fileSize = (Get-Item $exePath).Length / 1MB
        Write-Host "文件大小: $([math]::Round($fileSize, 2)) MB" -ForegroundColor Cyan

        # 询问是否测试运行
        $testRun = Read-Host "是否测试运行exe文件？(y/n)"
        if ($testRun -eq "y" -or $testRun -eq "Y") {
            Write-Host "启动FileTransferServer.exe..." -ForegroundColor Green
            Start-Process -FilePath $exePath
            Write-Host "程序已启动，请检查是否正常运行" -ForegroundColor Green
        }
    } else {
        Write-Host "可执行文件未找到" -ForegroundColor Red
    }
} else {
    Write-Host "打包失败" -ForegroundColor Red
    Write-Host "请检查错误信息并重试" -ForegroundColor Yellow
}

Write-Host "打包流程完成" -ForegroundColor Green
