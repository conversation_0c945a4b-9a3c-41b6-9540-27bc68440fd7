#!/usr/bin/env python3
"""
关节控制组件
基于testsimple/urdf_viewer_standalone.py中的关节控制逻辑实现
简化版本，只保留关节控制功能，通过信号与主界面通信
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QWidget, QLabel, QPushButton,
    QSlider, QDoubleSpinBox, QScrollArea, QFrame, QGridLayout
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont


class JointControlWindow(QDialog):
    """关节控制窗口"""

    # 信号定义
    joint_value_changed = pyqtSignal(str, float)  # 关节名称, 关节角度
    
    def __init__(self, urdf_parser, parent=None):
        super().__init__(parent)

        # 存储URDF解析器
        self.urdf_parser = urdf_parser

        # 关节控制相关
        self.joint_values = {}  # 存储当前关节角度
        self.joint_controls = {}  # 存储关节控制UI组件

        # 初始化UI
        self.init_ui()

        # 创建关节控制器
        self.create_joint_controls()
    
    def init_ui(self):
        """初始化用户界面 - 简化版本，只保留关节控制"""
        # 设置窗口属性
        self.setWindowTitle("机器人关节运动控制")
        self.setGeometry(100, 100, 350, 600)
        self.setModal(False)  # 非模态对话框，允许与主窗口交互

        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # 标题
        title_label = QLabel("关节控制")
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # 控制按钮
        button_layout = QHBoxLayout()

        self.reset_joints_button = QPushButton("重置关节")
        self.reset_joints_button.clicked.connect(self.reset_joints)
        button_layout.addWidget(self.reset_joints_button)

        self.reset_view_button = QPushButton("重置视角")
        self.reset_view_button.clicked.connect(self.reset_camera)
        button_layout.addWidget(self.reset_view_button)

        main_layout.addLayout(button_layout)

        # 创建滚动区域用于关节控制器
        self.joint_scroll = QScrollArea()
        self.joint_scroll.setWidgetResizable(True)
        self.joint_scroll.setMinimumHeight(200)

        # 关节控制器容器
        self.joint_widget = QWidget()
        self.joint_layout = QVBoxLayout(self.joint_widget)
        self.joint_scroll.setWidget(self.joint_widget)

        main_layout.addWidget(self.joint_scroll)
    
    def display_robot_info(self):
        """显示机器人信息"""
        if not self.urdf_parser:
            return
            
        try:
            # 获取机器人基本信息
            robot_info = self.urdf_parser.get_robot_info()
            
            info_text = f"机器人名称: {robot_info['name']}\n"
            info_text += f"自由度: {robot_info['dof']}\n"
            info_text += f"连杆数量: {robot_info['num_links']}\n"
            info_text += f"关节数量: {robot_info['num_joints']}\n\n"
            
            # 获取可控制关节信息
            joints = self.urdf_parser.get_joints()
            movable_joints = [j for j in joints if j.get('type') in ['revolute', 'continuous', 'prismatic']]
            
            info_text += f"可控制关节 ({len(movable_joints)}):\n"
            for joint in movable_joints:
                joint_type = joint.get('type', 'unknown')
                info_text += f"• {joint['name']} ({joint_type})\n"
            
            self.info_label.setText(info_text)
            
        except Exception as e:
            self.info_label.setText(f"获取机器人信息失败:\n{str(e)}")
    
    def create_joint_controls(self):
        """创建关节控制器"""
        if not self.urdf_parser:
            return

        try:
            # 清除现有控制器
            for i in reversed(range(self.joint_layout.count())):
                child = self.joint_layout.itemAt(i).widget()
                if child:
                    child.setParent(None)

            self.joint_controls.clear()
            self.joint_values.clear()

            # 获取所有关节
            joints = self.urdf_parser.get_joints()

            # 为每个可动关节创建控制器
            movable_joints = [j for j in joints if j.get('type') in ['revolute', 'continuous', 'prismatic']]

            if not movable_joints:
                no_joints_label = QLabel("没有可控制的关节")
                no_joints_label.setAlignment(Qt.AlignCenter)
                self.joint_layout.addWidget(no_joints_label)
                return

            for joint in movable_joints:
                self._create_joint_control_widget(joint)

            self.joint_layout.addStretch()

        except Exception as e:
            error_label = QLabel(f"创建关节控制器失败:\n{str(e)}")
            error_label.setStyleSheet("color: red;")
            self.joint_layout.addWidget(error_label)

    def _create_joint_control_widget(self, joint):
        """为单个关节创建控制组件"""
        joint_frame = QFrame()
        joint_frame.setFrameStyle(QFrame.Box)
        joint_frame_layout = QGridLayout(joint_frame)

        # 关节名称标签
        name_label = QLabel(f"关节 {joint['name']}")
        name_label.setStyleSheet("font-weight: bold;")
        joint_frame_layout.addWidget(name_label, 0, 0, 1, 3)

        # 关节类型标签
        joint_type = joint.get('type', 'unknown')
        type_label = QLabel(f"类型: {joint_type}")
        type_label.setStyleSheet("font-size: 10px; color: gray;")
        joint_frame_layout.addWidget(type_label, 1, 0, 1, 3)

        # 获取关节限制
        limit = joint.get('limit', {})
        if joint_type == 'continuous':
            min_val, max_val = -6.28, 6.28  # -2π to 2π
        elif joint_type == 'prismatic':
            min_val = limit.get('lower', -1.0)
            max_val = limit.get('upper', 1.0)
        else:  # revolute
            min_val = limit.get('lower', -3.14159)
            max_val = limit.get('upper', 3.14159)

        # 创建限位标签
        min_label = QLabel(f"{min_val:.3f}")
        min_label.setAlignment(Qt.AlignCenter)
        min_label.setStyleSheet("color: #666; font-size: 10px;")

        max_label = QLabel(f"{max_val:.3f}")
        max_label.setAlignment(Qt.AlignCenter)
        max_label.setStyleSheet("color: #666; font-size: 10px;")

        # 滑块
        slider = QSlider(Qt.Horizontal)
        slider.setMinimum(int(min_val * 1000))  # 转换为整数，精度到0.001
        slider.setMaximum(int(max_val * 1000))
        slider.setValue(0)
        slider.setTickPosition(QSlider.TicksBelow)
        slider.setTickInterval(int((max_val - min_val) * 100))

        # 数值显示框 - 增大宽度
        spinbox = QDoubleSpinBox()
        spinbox.setMinimum(min_val)
        spinbox.setMaximum(max_val)
        spinbox.setValue(0.0)
        spinbox.setDecimals(3)
        spinbox.setSingleStep(0.01)
        spinbox.setMinimumWidth(120)  # 增大宽度从80到120

        # 连接信号
        joint_name = joint['name']
        slider.valueChanged.connect(lambda v, name=joint_name: self.on_slider_changed(name, v))
        spinbox.valueChanged.connect(lambda v, name=joint_name: self.on_spinbox_changed(name, v))

        # 优化布局 - 限位显示在slider两侧
        joint_frame_layout.addWidget(min_label, 2, 0)      # 左侧限位
        joint_frame_layout.addWidget(slider, 2, 1)         # 中间滑块
        joint_frame_layout.addWidget(max_label, 2, 2)      # 右侧限位
        joint_frame_layout.addWidget(spinbox, 2, 3)        # 右侧数值框

        # 范围标签
        range_label = QLabel(f"{min_val:.2f} ~ {max_val:.2f}")
        range_label.setStyleSheet("font-size: 9px; color: gray;")
        range_label.setAlignment(Qt.AlignCenter)
        joint_frame_layout.addWidget(range_label, 3, 0, 1, 3)

        self.joint_layout.addWidget(joint_frame)

        # 存储控件引用
        self.joint_controls[joint_name] = {
            'slider': slider,
            'spinbox': spinbox,
            'min': min_val,
            'max': max_val
        }
        self.joint_values[joint_name] = 0.0

    def on_slider_changed(self, joint_name, value):
        """滑块值改变事件处理"""
        # 转换滑块值到实际角度值
        actual_value = value / 1000.0
        self.joint_values[joint_name] = actual_value

        # 更新对应的数值框（避免循环调用）
        spinbox = self.joint_controls[joint_name]['spinbox']
        spinbox.blockSignals(True)
        spinbox.setValue(actual_value)
        spinbox.blockSignals(False)

        # 发射信号通知主界面更新3D模型
        self.joint_value_changed.emit(joint_name, actual_value)

    def on_spinbox_changed(self, joint_name, value):
        """数值框值改变事件处理"""
        self.joint_values[joint_name] = value

        # 更新对应的滑块（避免循环调用）
        slider = self.joint_controls[joint_name]['slider']
        slider.blockSignals(True)
        slider.setValue(int(value * 1000))
        slider.blockSignals(False)

        # 发射信号通知主界面更新3D模型
        self.joint_value_changed.emit(joint_name, value)

    def reset_joints(self):
        """重置所有关节角度为0"""
        for joint_name in self.joint_values:
            self.joint_values[joint_name] = 0.0

            # 更新UI控件
            if joint_name in self.joint_controls:
                controls = self.joint_controls[joint_name]

                # 更新滑块
                controls['slider'].blockSignals(True)
                controls['slider'].setValue(0)
                controls['slider'].blockSignals(False)

                # 更新数值框
                controls['spinbox'].blockSignals(True)
                controls['spinbox'].setValue(0.0)
                controls['spinbox'].blockSignals(False)

                # 发射信号通知主界面更新3D模型
                self.joint_value_changed.emit(joint_name, 0.0)

    def reset_camera(self):
        """重置相机 - 通过信号通知主界面"""
        # 这里可以添加重置相机的信号，如果需要的话
        pass
