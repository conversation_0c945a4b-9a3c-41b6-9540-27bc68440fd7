#!/usr/bin/env python3
"""
25AutoAssembly URDF解析模块
基于已验证的standalone版本逻辑，直接解析URDF XML文件，不依赖roboticstoolbox

严格参考testsimple/urdf_viewer_standalone.py中的SimpleURDFParser实现
"""

import os
import logging
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import xml.etree.ElementTree as ET

import numpy as np

# 获取日志记录器
logger = logging.getLogger(__name__)


class URDFParser:
    """
    URDF解析器类 - 基于standalone版本的SimpleURDFParser

    负责直接解析URDF XML文件，提取连杆和关节信息，计算坐标变换
    不依赖roboticstoolbox，使用已验证的解析逻辑
    """

    def __init__(self):
        """初始化URDF解析器"""
        self.links = []
        self.joints = []
        self.robot_name = ""
        self.urdf_dir = ""  # URDF文件所在目录
        self.link_dict = {}  # 按名称索引的links
        self.joint_dict = {}  # 按名称索引的joints
        self.parent_child_map = {}  # parent -> child 映射
        self.child_parent_map = {}  # child -> parent 映射
        self.urdf_path = None
        self._is_loaded = False

        logger.info("URDFParser初始化完成")
    
    def load_urdf(self, urdf_path: str) -> bool:
        """
        加载URDF文件并解析 - 基于standalone版本的parse_urdf逻辑

        Args:
            urdf_path (str): URDF文件路径

        Returns:
            bool: 加载成功返回True，失败返回False
        """
        try:
            # 验证文件路径
            urdf_file = Path(urdf_path).resolve()
            if not urdf_file.exists():
                logger.error(f"URDF文件不存在: {urdf_path} (绝对路径: {urdf_file})")
                return False

            if not urdf_file.suffix.lower() == '.urdf':
                logger.error(f"文件格式错误，需要.urdf文件: {urdf_path}")
                return False

            logger.info(f"开始加载URDF文件: {urdf_path}")
            logger.info(f"绝对路径: {urdf_file}")

            # 保存URDF文件所在目录，用于解析相对路径
            self.urdf_dir = str(urdf_file.parent)
            self.urdf_path = str(urdf_file)
            logger.info(f"URDF目录: {self.urdf_dir}")

            # 解析XML文件
            tree = ET.parse(str(urdf_file))
            root = tree.getroot()

            # 获取机器人名称
            self.robot_name = root.get('name', 'Unknown Robot')

            # 清空之前的数据
            self.links = []
            self.joints = []
            self.link_dict = {}
            self.joint_dict = {}
            self.parent_child_map = {}
            self.child_parent_map = {}

            # 解析links
            for link in root.findall('link'):
                link_info = self._parse_link(link)
                if link_info:
                    self.links.append(link_info)
                    self.link_dict[link_info['name']] = link_info

            # 解析joints
            for joint in root.findall('joint'):
                joint_info = self._parse_joint(joint)
                if joint_info:
                    self.joints.append(joint_info)
                    self.joint_dict[joint_info['name']] = joint_info
                    # 建立parent-child关系
                    parent = joint_info['parent']
                    child = joint_info['child']
                    if parent and child:
                        self.parent_child_map[parent] = child
                        self.child_parent_map[child] = parent

            self._is_loaded = True
            logger.info(f"URDF文件解析成功: {self.robot_name}")
            logger.info(f"找到 {len(self.links)} 个连杆和 {len(self.joints)} 个关节")

            return True

        except Exception as e:
            logger.error(f"加载URDF文件失败: {e}")
            logger.error(f"错误类型: {type(e).__name__}")
            self._is_loaded = False
            return False

    def _parse_link(self, link_element):
        """解析link元素 - 基于standalone版本"""
        link_info = {
            'name': link_element.get('name'),
            'visual': [],
            'collision': []
        }

        # 解析visual元素
        for visual in link_element.findall('visual'):
            visual_info = self._parse_visual(visual)
            if visual_info:
                link_info['visual'].append(visual_info)

        return link_info

    def _parse_visual(self, visual_element):
        """解析visual元素 - 基于standalone版本"""
        visual_info = {
            'origin': [0, 0, 0, 0, 0, 0],  # x, y, z, roll, pitch, yaw
            'geometry': None
        }

        # 解析origin
        origin = visual_element.find('origin')
        if origin is not None:
            xyz = origin.get('xyz', '0 0 0').split()
            rpy = origin.get('rpy', '0 0 0').split()
            visual_info['origin'] = [float(x) for x in xyz] + [float(r) for r in rpy]

        # 解析geometry
        geometry = visual_element.find('geometry')
        if geometry is not None:
            visual_info['geometry'] = self._parse_geometry(geometry)

        return visual_info

    def _parse_geometry(self, geometry_element):
        """解析geometry元素 - 基于standalone版本"""
        # 检查box
        box = geometry_element.find('box')
        if box is not None:
            size = box.get('size', '1 1 1').split()
            return {'type': 'box', 'size': [float(s) for s in size]}

        # 检查cylinder
        cylinder = geometry_element.find('cylinder')
        if cylinder is not None:
            radius = float(cylinder.get('radius', '1'))
            length = float(cylinder.get('length', '1'))
            return {'type': 'cylinder', 'radius': radius, 'length': length}

        # 检查sphere
        sphere = geometry_element.find('sphere')
        if sphere is not None:
            radius = float(sphere.get('radius', '1'))
            return {'type': 'sphere', 'radius': radius}

        # 检查mesh
        mesh = geometry_element.find('mesh')
        if mesh is not None:
            filename = mesh.get('filename', '')
            # 将相对路径转换为绝对路径
            if filename and not os.path.isabs(filename):
                filename = os.path.join(self.urdf_dir, filename)
            return {'type': 'mesh', 'filename': filename}

        return None

    def _parse_joint(self, joint_element):
        """解析joint元素 - 基于standalone版本"""
        joint_info = {
            'name': joint_element.get('name'),
            'type': joint_element.get('type'),
            'parent': '',
            'child': '',
            'origin': {'xyz': [0, 0, 0], 'rpy': [0, 0, 0]},
            'limit': {}
        }

        # 解析parent和child
        parent = joint_element.find('parent')
        if parent is not None:
            joint_info['parent'] = parent.get('link', '')

        child = joint_element.find('child')
        if child is not None:
            joint_info['child'] = child.get('link', '')

        # 解析origin
        origin = joint_element.find('origin')
        if origin is not None:
            xyz_str = origin.get('xyz', '0 0 0')
            rpy_str = origin.get('rpy', '0 0 0')

            try:
                joint_info['origin']['xyz'] = [float(x) for x in xyz_str.split()]
                joint_info['origin']['rpy'] = [float(x) for x in rpy_str.split()]
            except ValueError:
                logger.warning(f"Invalid origin values for joint {joint_info['name']}")

        # 解析axis - 基于standalone版本
        axis = joint_element.find('axis')
        if axis is not None:
            xyz_str = axis.get('xyz', '0 0 1')
            try:
                joint_info['axis'] = [float(x) for x in xyz_str.split()]
            except ValueError:
                logger.warning(f"Invalid axis values for joint {joint_info['name']}")

        # 解析limit - 基于standalone版本
        limit = joint_element.find('limit')
        if limit is not None:
            try:
                joint_info['limit']['lower'] = float(limit.get('lower', '-3.14159'))
                joint_info['limit']['upper'] = float(limit.get('upper', '3.14159'))
                joint_info['limit']['effort'] = float(limit.get('effort', '0'))
                joint_info['limit']['velocity'] = float(limit.get('velocity', '0'))
            except ValueError:
                logger.warning(f"Invalid limit values for joint {joint_info['name']}")

        return joint_info

    def get_transform_chain(self, link_name, joint_values=None):
        """获取从根链接到指定链接的变换链 - 基于standalone版本"""
        if joint_values is None:
            joint_values = {}

        transforms = []
        current_link = link_name

        # 从子链接向父链接回溯
        while current_link in self.child_parent_map:
            parent_link = self.child_parent_map[current_link]
            # 找到连接这两个链接的关节
            joint = None
            for j in self.joints:
                if j['parent'] == parent_link and j['child'] == current_link:
                    joint = j
                    break

            if joint:
                # 创建包含关节角度的变换信息
                transform_info = {
                    'origin': joint['origin'],
                    'joint': joint,
                    'joint_value': joint_values.get(joint['name'], 0.0)
                }
                transforms.insert(0, transform_info)  # 插入到开头，保持正确顺序

            current_link = parent_link

        return transforms

    def compute_cumulative_transform(self, transforms):
        """计算累积变换矩阵 - 基于standalone版本"""
        # 初始化为单位矩阵
        cumulative_transform = np.eye(4)

        for transform_info in transforms:
            if isinstance(transform_info, dict) and 'origin' in transform_info:
                # 新格式：包含关节信息
                origin = transform_info['origin']
                joint = transform_info.get('joint')
                joint_value = transform_info.get('joint_value', 0.0)

                xyz = origin['xyz']
                rpy = origin['rpy']

                # 创建基础变换矩阵
                transform_matrix = self.create_transform_matrix(xyz, rpy)

                # 如果是可动关节，应用关节变换
                if joint:
                    if joint['type'] in ['revolute', 'continuous']:
                        # 旋转关节处理
                        if 'axis' in joint:
                            joint_rotation = self.create_joint_rotation_matrix(joint['axis'], joint_value)
                            transform_matrix = np.dot(transform_matrix, joint_rotation)
                        else:
                            # 如果没有axis字段，使用默认Z轴
                            logger.warning(f"关节 {joint['name']} 缺少axis字段，使用默认Z轴 [0, 0, 1]")
                            default_axis = [0, 0, 1]
                            joint_rotation = self.create_joint_rotation_matrix(default_axis, joint_value)
                            transform_matrix = np.dot(transform_matrix, joint_rotation)

                    elif joint['type'] == 'prismatic':
                        # 平移关节处理
                        print(f"🔧 [URDF] 处理平移关节: {joint['name']}, 值: {joint_value}")
                        if 'axis' in joint:
                            print(f"🔧 [URDF] 平移轴: {joint['axis']}")
                            joint_translation = self.create_joint_translation_matrix(joint['axis'], joint_value)
                            transform_matrix = np.dot(transform_matrix, joint_translation)
                            print(f"🔧 [URDF] 平移变换已应用")
                        else:
                            # 如果没有axis字段，使用默认Z轴
                            print(f"⚠️ [URDF] 平移关节 {joint['name']} 缺少axis字段，使用默认Z轴")
                            logger.warning(f"平移关节 {joint['name']} 缺少axis字段，使用默认Z轴 [0, 0, 1]")
                            default_axis = [0, 0, 1]
                            joint_translation = self.create_joint_translation_matrix(default_axis, joint_value)
                            transform_matrix = np.dot(transform_matrix, joint_translation)

            else:
                # 旧格式：兼容性处理
                xyz = transform_info['xyz']
                rpy = transform_info['rpy']
                transform_matrix = self.create_transform_matrix(xyz, rpy)

            # 累积变换
            cumulative_transform = np.dot(cumulative_transform, transform_matrix)

        return cumulative_transform

    def create_joint_rotation_matrix(self, axis, angle):
        """根据关节轴和角度创建旋转矩阵 - 基于standalone版本"""
        # 归一化轴向量
        axis = np.array(axis)
        axis = axis / np.linalg.norm(axis)

        # 使用罗德里格旋转公式创建旋转矩阵
        cos_angle = np.cos(angle)
        sin_angle = np.sin(angle)

        # 反对称矩阵
        K = np.array([
            [0, -axis[2], axis[1]],
            [axis[2], 0, -axis[0]],
            [-axis[1], axis[0], 0]
        ])

        # 罗德里格公式: R = I + sin(θ)K + (1-cos(θ))K²
        R = np.eye(3) + sin_angle * K + (1 - cos_angle) * np.dot(K, K)

        # 创建4x4变换矩阵
        T = np.eye(4)
        T[:3, :3] = R

        return T

    def create_transform_matrix(self, xyz, rpy):
        """根据xyz和rpy创建4x4变换矩阵 - 基于standalone版本"""
        x, y, z = xyz
        roll, pitch, yaw = rpy

        # 创建旋转矩阵 (ZYX欧拉角)
        cos_r, sin_r = np.cos(roll), np.sin(roll)
        cos_p, sin_p = np.cos(pitch), np.sin(pitch)
        cos_y, sin_y = np.cos(yaw), np.sin(yaw)

        # 旋转矩阵 R = Rz(yaw) * Ry(pitch) * Rx(roll)
        R = np.array([
            [cos_y*cos_p, cos_y*sin_p*sin_r - sin_y*cos_r, cos_y*sin_p*cos_r + sin_y*sin_r],
            [sin_y*cos_p, sin_y*sin_p*sin_r + cos_y*cos_r, sin_y*sin_p*cos_r - cos_y*sin_r],
            [-sin_p, cos_p*sin_r, cos_p*cos_r]
        ])

        # 创建4x4变换矩阵
        T = np.eye(4)
        T[:3, :3] = R
        T[:3, 3] = [x, y, z]

        return T

    def create_joint_translation_matrix(self, axis, distance):
        """根据关节轴和距离创建平移矩阵"""
        # 归一化轴向量
        axis = np.array(axis)
        axis = axis / np.linalg.norm(axis)

        # 创建4x4平移变换矩阵
        T = np.eye(4)
        T[:3, 3] = axis * distance

        return T

    def get_robot_info(self) -> Dict[str, Any]:
        """
        获取机器人的基本信息 - 基于新的数据结构

        Returns:
            Dict[str, Any]: 包含机器人基本信息的字典
        """
        if not self._is_loaded:
            return {}

        return {
            'name': self.robot_name,
            'dof': len([j for j in self.joints if j['type'] in ['revolute', 'prismatic']]),
            'num_links': len(self.links),
            'num_joints': len(self.joints),
            'urdf_path': self.urdf_path,
            'joint_names': [joint['name'] for joint in self.joints],
            'link_names': [link['name'] for link in self.links],
            'joint_types': [joint['type'] for joint in self.joints]
        }
    
    def get_dof(self) -> int:
        """
        获取机器人的自由度数量 - 基于新的数据结构

        Returns:
            int: 自由度数量
        """
        if not self._is_loaded:
            logger.warning("机器人模型未加载，无法获取自由度")
            return 0

        # 计算可动关节的数量（revolute和prismatic）
        return len([j for j in self.joints if j['type'] in ['revolute', 'prismatic']])
    
    def is_loaded(self) -> bool:
        """
        检查URDF是否已加载

        Returns:
            bool: 已加载返回True，否则返回False
        """
        return self._is_loaded

    def get_links(self) -> List[Dict[str, Any]]:
        """
        获取所有连杆信息

        Returns:
            List[Dict[str, Any]]: 连杆信息列表
        """
        return self.links

    def get_joints(self) -> List[Dict[str, Any]]:
        """
        获取所有关节信息

        Returns:
            List[Dict[str, Any]]: 关节信息列表
        """
        return self.joints
