# 25AutoAssembly 项目完整总结

## 📋 项目概述

**25AutoAssembly** 是一个基于PyQt5的光学设备精密运动控制系统，用于控制多台六自由度运动平台，实现自动装配和位姿调整功能。

### 核心功能
- **TCP通信控制**: 与运动控制计算机进行实时通信
- **ZFR数据分析**: 自动读取和解析光学测量数据
- **6轴运动控制**: 精密控制X,Y,Z平移和A,B,C旋转
- **实时位姿监控**: 100ms频率的位姿数据更新
- **图形化界面**: 四层布局的用户友好界面

## 🏗️ 系统架构

### 核心模块结构
```
25AutoAssembly/
├── gui/                    # 用户界面层
│   └── motion_control_ui.py
├── device_communication/   # 设备通信层
│   └── tcp_motion_controller.py
├── data_models/           # 数据模型层
│   └── zfr_data.py
├── file_handler/          # 文件处理层
│   └── result_parser.py
├── config/               # 配置管理
│   ├── settings.py
│   └── config.json
├── utils/                # 工具模块
│   └── logger.py
├── main.py              # 程序入口
└── run.py               # 快速启动
```

### 技术栈
- **GUI框架**: PyQt5
- **通信协议**: TCP Socket + 自定义帧格式
- **数据格式**: JSON
- **文件监控**: watchdog
- **数值计算**: numpy

## 🔧 核心功能模块

### 1. TCP运动控制器 (TCPMotionController)
**文件**: `device_communication/tcp_motion_controller.py`

#### 主要功能
- **设备管理**: 支持3台设备 (1号六自由度台、2号六自由度台、3号六自由度台)
- **通信协议**: 
  - 发送: `***********MOV X0.001 Y0.001 Z0.001 A0.001 B0.001 C0.001`
  - 接收: `$Frame{"devices":[{"id":"device1","X":0,"Y":0,"Z":0,"A":0,"B":0,"C":0,"M1":0}]}$End`
- **实时数据**: 100ms频率的位姿数据接收
- **错误处理**: 完整的异常处理和重连机制

#### 关键方法
- `connect()`: 建立TCP连接
- `send_motion_command()`: 发送6轴运动指令
- `_receive_loop()`: 接收数据循环
- `_process_buffer()`: 帧解析处理

### 2. 运动控制界面 (MotionControlMainWindow)
**文件**: `gui/motion_control_ui.py`

#### 四层布局设计
1. **第一层**: 连接控制 + ZFR数据显示
2. **第二层**: 设备选择 + 位姿显示
3. **第三层**: 运动参数 + 自动控制
4. **第四层**: 系统日志

#### 核心功能
- **ZFR数据分析**: 根据Power、Coma_X、Coma_Y计算运动量
- **运动控制**: 6轴精密运动控制
- **实时监控**: 位姿数据实时显示
- **参数配置**: 运动等级和比例因子设置

### 3. ZFR数据模型 (ZFRData)
**文件**: `data_models/zfr_data.py`

#### 数据结构
```python
@dataclass
class ZFRData:
    power: float = 0.0      # 功率值
    coma_x: float = 0.0     # X方向彗差
    coma_y: float = 0.0     # Y方向彗差
    pv: float = 0.0         # PV值
    rms: float = 0.0        # RMS值
```

### 4. 文件解析器 (ResultParser)
**文件**: `file_handler/result_parser.py`

#### 功能
- 解析`resource/result.txt`文件
- 提取ZFR测量数据
- 支持实时文件监控

## 🔄 运动控制算法

### 运动规则
1. **Power值控制Z轴**: 
   - Power > 0: Z轴正向移动 (运动等级 × 比例因子)
   - Power < 0: Z轴负向移动

2. **Coma_X控制X轴和B轴**:
   - Coma_X > 0: X轴正向 + B轴负向旋转
   - Coma_X < 0: X轴负向 + B轴正向旋转

3. **Coma_Y控制Y轴和A轴**:
   - Coma_Y > 0: Y轴负向 + A轴负向旋转
   - Coma_Y < 0: Y轴正向 + A轴正向旋转

### 参数配置
- **运动等级**: 0.001 - 1.0 (可调节精度)
- **比例因子**: 1 - 10 (整数，控制平移旋转比例)

## 📡 通信协议详解

### TCP连接
- **模式**: 客户端模式 (25AutoAssembly → 运动控制计算机)
- **连接**: 长连接，实时数据交换
- **IP映射**: 
  - device1 → *********** (1号六自由度台)
  - device2 → *********** (2号六自由度台)
  - device3 → *********** (3号六自由度台)

### 数据格式
#### 发送指令
```
***********MOV X0.001000 Y0.001000 Z0.001000 A0.001000 B0.001000 C0.001000
***********STOP
***********QUERY
```

#### 接收数据
```json
$Frame{
  "devices": [
    {
      "id": "device1",
      "A": 0.123456,
      "B": 0.234567,
      "C": 0.345678,
      "X": 1.234567,
      "Y": 2.345678,
      "Z": 3.456789,
      "M1": 0
    }
  ]
}$End
```

## 🗂️ 文件结构分析

### 核心文件 (必需)
- `main.py` - 程序主入口
- `run.py` - 快速启动脚本
- `gui/motion_control_ui.py` - 主界面
- `device_communication/tcp_motion_controller.py` - TCP控制器
- `data_models/zfr_data.py` - 数据模型
- `file_handler/result_parser.py` - 文件解析
- `config/settings.py` - 配置管理
- `utils/logger.py` - 日志工具

### 配置文件
- `config/config.json` - 主配置文件
- `requirements.txt` - Python依赖
- `environment.yml` - Conda环境

### 文档文件
- `README.md` - 项目说明
- `TCP_INTEGRATION_COMPLETE.md` - TCP集成报告
- `PROJECT_FINAL_SUMMARY.md` - 项目总结 (本文件)

### 参考文件 (resource/)
- `resource/TcpServerForBUAA.py` - TCP通信参考实现
- `resource/settings.py` - 配置参考
- `resource/settings.ini` - 配置文件示例
- `resource/result.txt` - ZFR数据文件

## 🚀 使用方法

### 启动程序
```bash
# 方法1: 使用主程序
python main.py

# 方法2: 使用快速启动
python run.py
```

### 操作流程
1. **连接设备**: 输入运动控制计算机IP和端口
2. **选择设备**: 选择要控制的六自由度台
3. **加载数据**: 自动读取ZFR数据文件
4. **分析计算**: 根据ZFR值计算运动参数
5. **执行运动**: 发送运动指令到设备
6. **监控位姿**: 实时查看设备位姿变化

## ✅ 项目特点

### 技术优势
- **模块化设计**: 清晰的层次结构和职责分离
- **实时通信**: 100ms频率的位姿数据更新
- **错误处理**: 完整的异常处理和用户提示
- **可扩展性**: 支持多设备并发控制
- **用户友好**: 直观的四层界面布局

### 安全特性
- **确认机制**: 运动执行前需要用户确认
- **紧急停止**: 一键停止所有设备运动
- **状态监控**: 实时显示连接和设备状态
- **日志记录**: 详细的操作日志和错误记录

## 📊 项目状态

### 已完成功能 ✅
- TCP通信协议集成
- 四层界面布局
- ZFR数据解析
- 6轴运动控制
- 实时位姿监控
- 设备管理
- 错误处理

### 测试状态
- ✅ 界面启动测试
- ✅ 模块导入测试
- ✅ 配置加载测试
- 🔄 TCP通信测试 (需要硬件环境)
- 🔄 运动控制测试 (需要硬件环境)

## 🔧 维护说明

### 依赖管理
- Python 3.7+
- PyQt5
- numpy
- watchdog

### 配置文件
- 主配置: `config/config.json`
- 网络配置: 在界面中设置
- 设备映射: `device_communication/tcp_motion_controller.py`

### 日志文件
- 系统日志: `logs/system.log`
- 界面日志: 实时显示在界面中

项目已完成核心功能开发，准备进行硬件集成测试！
