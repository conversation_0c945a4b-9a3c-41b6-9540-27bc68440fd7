#!/usr/bin/env python3
"""
URDF Visualizer - Standalone Version
不依赖roboticstoolbox，直接解析URDF文件，使用VTK进行3D可视化，PyQt5作为GUI框架
"""

import sys
import os
import numpy as np
import xml.etree.ElementTree as ET
from pathlib import Path

# PyQt5 imports
try:
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                                QPushButton, QWidget, QFileDialog, QMessageBox, QLabel, QTextEdit,
                                QSlider, QDoubleSpinBox, QScrollArea, QFrame, QGridLayout, QGroupBox)
    from PyQt5.QtCore import Qt, pyqtSignal
    PYQT_AVAILABLE = True
    print("PyQt5 loaded successfully")
except ImportError as e:
    print(f"PyQt5 not available: {e}")
    PYQT_AVAILABLE = False
    sys.exit(1)

# VTK imports
VTK_AVAILABLE = False
QVTK_AVAILABLE = False
try:
    import vtk
    print(f"VTK version: {vtk.vtkVersion.GetVTKVersion()}")
    VTK_AVAILABLE = True
    
    # 尝试导入Qt集成组件
    try:
        from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
        QVTK_AVAILABLE = True
        print("VTK-Qt integration available")
    except ImportError as e:
        print(f"VTK-Qt integration not available: {e}")
                
except ImportError as e:
    print(f"VTK not available: {e}")
    VTK_AVAILABLE = False

class SimpleURDFParser:
    """简单的URDF解析器"""

    def __init__(self):
        self.links = []
        self.joints = []
        self.robot_name = ""
        self.urdf_dir = ""  # URDF文件所在目录
        self.link_dict = {}  # 按名称索引的links
        self.joint_dict = {}  # 按名称索引的joints
        self.parent_child_map = {}  # parent -> child 映射
        self.child_parent_map = {}  # child -> parent 映射
        
    def parse_urdf(self, filename):
        """解析URDF文件"""
        try:
            # 保存URDF文件所在目录，用于解析相对路径
            self.urdf_dir = os.path.dirname(os.path.abspath(filename))
            print(f"URDF directory: {self.urdf_dir}")

            tree = ET.parse(filename)
            root = tree.getroot()

            # 获取机器人名称
            self.robot_name = root.get('name', 'Unknown Robot')

            # 解析links
            for link in root.findall('link'):
                link_info = self.parse_link(link)
                if link_info:
                    self.links.append(link_info)
                    self.link_dict[link_info['name']] = link_info

            # 解析joints
            for joint in root.findall('joint'):
                joint_info = self.parse_joint(joint)
                if joint_info:
                    self.joints.append(joint_info)
                    self.joint_dict[joint_info['name']] = joint_info
                    # 建立parent-child关系
                    parent = joint_info['parent']
                    child = joint_info['child']
                    if parent and child:
                        self.parent_child_map[parent] = child
                        self.child_parent_map[child] = parent

            print(f"Parsed URDF: {self.robot_name}")
            print(f"Found {len(self.links)} links and {len(self.joints)} joints")

            return True

        except Exception as e:
            print(f"Error parsing URDF: {e}")
            return False
    
    def parse_link(self, link_element):
        """解析link元素"""
        link_info = {
            'name': link_element.get('name'),
            'visual': [],
            'collision': []
        }
        
        # 解析visual元素
        for visual in link_element.findall('visual'):
            visual_info = self.parse_visual(visual)
            if visual_info:
                link_info['visual'].append(visual_info)
        
        return link_info
    
    def parse_visual(self, visual_element):
        """解析visual元素"""
        visual_info = {
            'origin': [0, 0, 0, 0, 0, 0],  # x, y, z, roll, pitch, yaw
            'geometry': None
        }
        
        # 解析origin
        origin = visual_element.find('origin')
        if origin is not None:
            xyz = origin.get('xyz', '0 0 0').split()
            rpy = origin.get('rpy', '0 0 0').split()
            visual_info['origin'] = [float(x) for x in xyz] + [float(r) for r in rpy]
        
        # 解析geometry
        geometry = visual_element.find('geometry')
        if geometry is not None:
            visual_info['geometry'] = self.parse_geometry(geometry)
        
        return visual_info
    
    def parse_geometry(self, geometry_element):
        """解析geometry元素"""
        # 检查box
        box = geometry_element.find('box')
        if box is not None:
            size = box.get('size', '1 1 1').split()
            return {'type': 'box', 'size': [float(s) for s in size]}
        
        # 检查cylinder
        cylinder = geometry_element.find('cylinder')
        if cylinder is not None:
            radius = float(cylinder.get('radius', '1'))
            length = float(cylinder.get('length', '1'))
            return {'type': 'cylinder', 'radius': radius, 'length': length}
        
        # 检查sphere
        sphere = geometry_element.find('sphere')
        if sphere is not None:
            radius = float(sphere.get('radius', '1'))
            return {'type': 'sphere', 'radius': radius}
        
        # 检查mesh
        mesh = geometry_element.find('mesh')
        if mesh is not None:
            filename = mesh.get('filename', '')
            # 将相对路径转换为绝对路径
            if filename and not os.path.isabs(filename):
                filename = os.path.join(self.urdf_dir, filename)
            return {'type': 'mesh', 'filename': filename}
        
        return None
    
    def parse_joint(self, joint_element):
        """解析joint元素"""
        joint_info = {
            'name': joint_element.get('name'),
            'type': joint_element.get('type'),
            'parent': '',
            'child': '',
            'origin': {'xyz': [0, 0, 0], 'rpy': [0, 0, 0]},
            'axis': [0, 0, 1],  # 默认z轴
            'limit': {'lower': -3.14159, 'upper': 3.14159, 'effort': 0, 'velocity': 0}
        }

        # 解析parent和child
        parent = joint_element.find('parent')
        if parent is not None:
            joint_info['parent'] = parent.get('link', '')

        child = joint_element.find('child')
        if child is not None:
            joint_info['child'] = child.get('link', '')

        # 解析origin
        origin = joint_element.find('origin')
        if origin is not None:
            xyz_str = origin.get('xyz', '0 0 0')
            rpy_str = origin.get('rpy', '0 0 0')

            try:
                joint_info['origin']['xyz'] = [float(x) for x in xyz_str.split()]
                joint_info['origin']['rpy'] = [float(x) for x in rpy_str.split()]
            except ValueError:
                print(f"Warning: Invalid origin values for joint {joint_info['name']}")

        # 解析axis
        axis = joint_element.find('axis')
        if axis is not None:
            xyz_str = axis.get('xyz', '0 0 1')
            try:
                joint_info['axis'] = [float(x) for x in xyz_str.split()]
            except ValueError:
                print(f"Warning: Invalid axis values for joint {joint_info['name']}")

        # 解析limit
        limit = joint_element.find('limit')
        if limit is not None:
            try:
                joint_info['limit']['lower'] = float(limit.get('lower', '-3.14159'))
                joint_info['limit']['upper'] = float(limit.get('upper', '3.14159'))
                joint_info['limit']['effort'] = float(limit.get('effort', '0'))
                joint_info['limit']['velocity'] = float(limit.get('velocity', '0'))
            except ValueError:
                print(f"Warning: Invalid limit values for joint {joint_info['name']}")

        return joint_info

    def get_transform_chain(self, link_name, joint_values=None):
        """获取从根链接到指定链接的变换链"""
        if joint_values is None:
            joint_values = {}

        transforms = []
        current_link = link_name

        # 从子链接向父链接回溯
        while current_link in self.child_parent_map:
            parent_link = self.child_parent_map[current_link]
            # 找到连接这两个链接的关节
            joint = None
            for j in self.joints:
                if j['parent'] == parent_link and j['child'] == current_link:
                    joint = j
                    break

            if joint:
                # 创建包含关节角度的变换信息
                transform_info = {
                    'origin': joint['origin'],
                    'joint': joint,
                    'joint_value': joint_values.get(joint['name'], 0.0)
                }
                transforms.insert(0, transform_info)  # 插入到开头，保持正确顺序

            current_link = parent_link

        return transforms

    def compute_cumulative_transform(self, transforms):
        """计算累积变换矩阵"""
        import numpy as np

        # 初始化为单位矩阵
        cumulative_transform = np.eye(4)

        for transform_info in transforms:
            if isinstance(transform_info, dict) and 'origin' in transform_info:
                # 新格式：包含关节信息
                origin = transform_info['origin']
                joint = transform_info.get('joint')
                joint_value = transform_info.get('joint_value', 0.0)

                xyz = origin['xyz']
                rpy = origin['rpy']

                # 创建基础变换矩阵
                transform_matrix = self.create_transform_matrix(xyz, rpy)

                # 如果是可动关节，应用关节角度
                if joint and joint['type'] in ['revolute', 'continuous']:
                    joint_rotation = self.create_joint_rotation_matrix(joint['axis'], joint_value)
                    transform_matrix = np.dot(transform_matrix, joint_rotation)

            else:
                # 旧格式：兼容性处理
                xyz = transform_info['xyz']
                rpy = transform_info['rpy']
                transform_matrix = self.create_transform_matrix(xyz, rpy)

            # 累积变换
            cumulative_transform = np.dot(cumulative_transform, transform_matrix)

        return cumulative_transform

    def create_transform_matrix(self, xyz, rpy):
        """根据xyz和rpy创建4x4变换矩阵"""
        import numpy as np

        x, y, z = xyz
        roll, pitch, yaw = rpy

        # 创建旋转矩阵 (ZYX欧拉角)
        cos_r, sin_r = np.cos(roll), np.sin(roll)
        cos_p, sin_p = np.cos(pitch), np.sin(pitch)
        cos_y, sin_y = np.cos(yaw), np.sin(yaw)

        # 旋转矩阵 R = Rz(yaw) * Ry(pitch) * Rx(roll)
        R = np.array([
            [cos_y*cos_p, cos_y*sin_p*sin_r - sin_y*cos_r, cos_y*sin_p*cos_r + sin_y*sin_r],
            [sin_y*cos_p, sin_y*sin_p*sin_r + cos_y*cos_r, sin_y*sin_p*cos_r - cos_y*sin_r],
            [-sin_p, cos_p*sin_r, cos_p*cos_r]
        ])

        # 创建4x4变换矩阵
        T = np.eye(4)
        T[:3, :3] = R
        T[:3, 3] = [x, y, z]

        return T

    def create_joint_rotation_matrix(self, axis, angle):
        """根据关节轴和角度创建旋转矩阵"""
        import numpy as np

        # 归一化轴向量
        axis = np.array(axis)
        axis = axis / np.linalg.norm(axis)

        # 使用罗德里格旋转公式创建旋转矩阵
        cos_angle = np.cos(angle)
        sin_angle = np.sin(angle)

        # 反对称矩阵
        K = np.array([
            [0, -axis[2], axis[1]],
            [axis[2], 0, -axis[0]],
            [-axis[1], axis[0], 0]
        ])

        # 罗德里格公式: R = I + sin(θ)K + (1-cos(θ))K²
        R = np.eye(3) + sin_angle * K + (1 - cos_angle) * np.dot(K, K)

        # 创建4x4变换矩阵
        T = np.eye(4)
        T[:3, :3] = R

        return T

class URDFVisualizerStandalone(QMainWindow):
    """独立版URDF可视化器"""

    def __init__(self):
        super().__init__()
        self.parser = SimpleURDFParser()
        self.vtk_actors = []
        self.joint_values = {}  # 存储当前关节角度
        self.joint_controls = {}  # 存储关节控制UI组件
        self.link_actors = {}  # 存储每个link对应的actors
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("URDF Visualizer - Standalone Version (No roboticstoolbox)")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 左侧控制面板
        left_panel = QWidget()
        left_panel.setMaximumWidth(300)
        left_layout = QVBoxLayout(left_panel)
        
        # 控制按钮
        self.open_button = QPushButton("Open URDF File")
        self.open_button.clicked.connect(self.open_urdf_file)
        left_layout.addWidget(self.open_button)
        
        self.reset_button = QPushButton("Reset View")
        self.reset_button.clicked.connect(self.reset_camera)
        left_layout.addWidget(self.reset_button)

        self.reset_joints_button = QPushButton("Reset Joints")
        self.reset_joints_button.clicked.connect(self.reset_joints)
        left_layout.addWidget(self.reset_joints_button)
        
        # 信息显示区域
        info_label = QLabel("URDF Information:")
        left_layout.addWidget(info_label)
        
        self.info_text = QTextEdit()
        self.info_text.setMaximumHeight(200)
        left_layout.addWidget(self.info_text)
        
        # 关节控制面板
        joint_group = QGroupBox("关节控制")
        joint_layout = QVBoxLayout(joint_group)

        # 创建滚动区域用于关节控制器
        self.joint_scroll = QScrollArea()
        self.joint_scroll.setWidgetResizable(True)
        self.joint_scroll.setMaximumHeight(300)

        # 关节控制器容器
        self.joint_widget = QWidget()
        self.joint_layout = QVBoxLayout(self.joint_widget)
        self.joint_scroll.setWidget(self.joint_widget)

        joint_layout.addWidget(self.joint_scroll)
        left_layout.addWidget(joint_group)

        # 状态标签
        self.status_label = QLabel("Ready - Click 'Open URDF' to load a robot model")
        self.status_label.setWordWrap(True)
        left_layout.addWidget(self.status_label)

        left_layout.addStretch()
        main_layout.addWidget(left_panel)
        
        # 右侧VTK渲染区域
        if QVTK_AVAILABLE:
            self.vtk_widget = QVTKRenderWindowInteractor(central_widget)
            main_layout.addWidget(self.vtk_widget)
            self.setup_vtk_renderer()
        else:
            placeholder = QLabel("VTK 3D Viewer\n(VTK-Qt integration not available)")
            placeholder.setStyleSheet("background-color: #f0f0f0; border: 1px solid #ccc; padding: 20px;")
            placeholder.setAlignment(Qt.AlignCenter)
            main_layout.addWidget(placeholder)
        
    def setup_vtk_renderer(self):
        """设置VTK渲染器"""
        if not (VTK_AVAILABLE and QVTK_AVAILABLE):
            return
            
        try:
            # 创建渲染器
            self.renderer = vtk.vtkRenderer()
            self.renderer.SetBackground(0.2, 0.3, 0.4)
            
            # 添加到渲染窗口
            self.vtk_widget.GetRenderWindow().AddRenderer(self.renderer)
            
            # 添加坐标轴
            axes = vtk.vtkAxesActor()
            axes.SetTotalLength(0.3, 0.3, 0.3)
            self.renderer.AddActor(axes)
            
            # 添加网格
            self.add_grid()
            
            print("VTK renderer setup completed")
        except Exception as e:
            print(f"Error setting up VTK renderer: {e}")
    
    def add_grid(self):
        """添加网格"""
        try:
            # 创建网格平面
            plane = vtk.vtkPlaneSource()
            plane.SetXResolution(10)
            plane.SetYResolution(10)
            plane.SetOrigin(-1, -1, 0)
            plane.SetPoint1(1, -1, 0)
            plane.SetPoint2(-1, 1, 0)
            
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputConnection(plane.GetOutputPort())
            
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            actor.GetProperty().SetRepresentationToWireframe()
            actor.GetProperty().SetColor(0.5, 0.5, 0.5)
            actor.GetProperty().SetOpacity(0.3)
            
            self.renderer.AddActor(actor)
        except Exception as e:
            print(f"Error adding grid: {e}")

    def create_joint_controls(self):
        """创建关节控制器"""
        # 清除现有控制器
        for i in reversed(range(self.joint_layout.count())):
            child = self.joint_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        self.joint_controls.clear()
        self.joint_values.clear()

        # 为每个可动关节创建控制器
        movable_joints = [j for j in self.parser.joints if j['type'] in ['revolute', 'continuous', 'prismatic']]

        if not movable_joints:
            no_joints_label = QLabel("没有可控制的关节")
            no_joints_label.setAlignment(Qt.AlignCenter)
            self.joint_layout.addWidget(no_joints_label)
            return

        for joint in movable_joints:
            joint_frame = QFrame()
            joint_frame.setFrameStyle(QFrame.Box)
            joint_frame_layout = QGridLayout(joint_frame)

            # 关节名称标签
            name_label = QLabel(f"关节 {joint['name']}")
            name_label.setStyleSheet("font-weight: bold;")
            joint_frame_layout.addWidget(name_label, 0, 0, 1, 3)

            # 关节类型标签
            type_label = QLabel(f"类型: {joint['type']}")
            type_label.setStyleSheet("font-size: 10px; color: gray;")
            joint_frame_layout.addWidget(type_label, 1, 0, 1, 3)

            # 获取关节限制
            if joint['type'] == 'continuous':
                min_val, max_val = -6.28, 6.28  # -2π to 2π
            elif joint['type'] == 'prismatic':
                min_val = joint['limit']['lower'] if joint['limit']['lower'] != -3.14159 else -1.0
                max_val = joint['limit']['upper'] if joint['limit']['upper'] != 3.14159 else 1.0
            else:  # revolute
                min_val, max_val = joint['limit']['lower'], joint['limit']['upper']

            # 滑块
            slider = QSlider(Qt.Horizontal)
            slider.setMinimum(int(min_val * 1000))  # 转换为整数，精度到0.001
            slider.setMaximum(int(max_val * 1000))
            slider.setValue(0)
            slider.setTickPosition(QSlider.TicksBelow)
            slider.setTickInterval(int((max_val - min_val) * 100))

            # 数值显示框
            spinbox = QDoubleSpinBox()
            spinbox.setMinimum(min_val)
            spinbox.setMaximum(max_val)
            spinbox.setValue(0.0)
            spinbox.setDecimals(3)
            spinbox.setSingleStep(0.01)
            spinbox.setMaximumWidth(80)

            # 连接信号
            slider.valueChanged.connect(lambda v, name=joint['name']: self.on_slider_changed(name, v))
            spinbox.valueChanged.connect(lambda v, name=joint['name']: self.on_spinbox_changed(name, v))

            # 布局
            joint_frame_layout.addWidget(slider, 2, 0, 1, 2)
            joint_frame_layout.addWidget(spinbox, 2, 2)

            # 范围标签
            range_label = QLabel(f"{min_val:.2f} ~ {max_val:.2f}")
            range_label.setStyleSheet("font-size: 9px; color: gray;")
            range_label.setAlignment(Qt.AlignCenter)
            joint_frame_layout.addWidget(range_label, 3, 0, 1, 3)

            self.joint_layout.addWidget(joint_frame)

            # 存储控件引用
            self.joint_controls[joint['name']] = {
                'slider': slider,
                'spinbox': spinbox,
                'min': min_val,
                'max': max_val
            }
            self.joint_values[joint['name']] = 0.0

        self.joint_layout.addStretch()

    def on_slider_changed(self, joint_name, value):
        """滑块值改变事件处理"""
        # 转换滑块值到实际角度值
        actual_value = value / 1000.0
        self.joint_values[joint_name] = actual_value

        # 更新对应的数值框（避免循环调用）
        spinbox = self.joint_controls[joint_name]['spinbox']
        spinbox.blockSignals(True)
        spinbox.setValue(actual_value)
        spinbox.blockSignals(False)

        # 更新3D模型
        self.update_robot_pose()

    def on_spinbox_changed(self, joint_name, value):
        """数值框值改变事件处理"""
        self.joint_values[joint_name] = value

        # 更新对应的滑块（避免循环调用）
        slider = self.joint_controls[joint_name]['slider']
        slider.blockSignals(True)
        slider.setValue(int(value * 1000))
        slider.blockSignals(False)

        # 更新3D模型
        self.update_robot_pose()

    def update_robot_pose(self):
        """根据当前关节角度更新机器人姿态"""
        if not (VTK_AVAILABLE and QVTK_AVAILABLE and hasattr(self, 'renderer')):
            return

        try:
            # 重新计算所有link的变换并更新actor位置
            for link_name, actors in self.link_actors.items():
                # 获取变换链
                transform_chain = self.parser.get_transform_chain(link_name, self.joint_values)
                cumulative_transform = self.parser.compute_cumulative_transform(transform_chain)

                # 更新每个actor的变换
                for actor_info in actors:
                    actor = actor_info['actor']
                    visual_transform = actor_info['visual_transform']

                    # 组合累积变换和visual变换
                    final_transform = np.dot(cumulative_transform, visual_transform)

                    # 创建VTK变换
                    transform = vtk.vtkTransform()
                    vtk_matrix = vtk.vtkMatrix4x4()
                    for row in range(4):
                        for col in range(4):
                            vtk_matrix.SetElement(row, col, final_transform[row, col])

                    transform.SetMatrix(vtk_matrix)
                    actor.SetUserTransform(transform)

            # 刷新渲染
            self.vtk_widget.GetRenderWindow().Render()

        except Exception as e:
            print(f"Error updating robot pose: {e}")

    def reset_joints(self):
        """重置所有关节角度为0"""
        for joint_name in self.joint_values:
            self.joint_values[joint_name] = 0.0

            # 更新UI控件
            if joint_name in self.joint_controls:
                controls = self.joint_controls[joint_name]

                # 更新滑块
                controls['slider'].blockSignals(True)
                controls['slider'].setValue(0)
                controls['slider'].blockSignals(False)

                # 更新数值框
                controls['spinbox'].blockSignals(True)
                controls['spinbox'].setValue(0.0)
                controls['spinbox'].blockSignals(False)

        # 更新3D模型
        self.update_robot_pose()

    def open_urdf_file(self):
        """打开URDF文件"""
        default_path = Path(__file__).parent.parent / "data" / "auboi5" / "aubo_i5.urdf"
        
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Open URDF File",
            str(default_path) if default_path.exists() else "",
            "URDF Files (*.urdf);;All Files (*)"
        )
        
        if file_path:
            self.load_urdf_file(file_path)
            
    def load_urdf_file(self, file_path):
        """加载URDF文件"""
        try:
            self.status_label.setText(f"Loading URDF file: {os.path.basename(file_path)}")
            QApplication.processEvents()
            
            # 解析URDF
            if self.parser.parse_urdf(file_path):
                # 显示信息
                self.display_urdf_info()

                # 创建关节控制器
                self.create_joint_controls()

                # 可视化
                if VTK_AVAILABLE and QVTK_AVAILABLE:
                    self.visualize_urdf()
                    self.reset_camera()

                self.status_label.setText(f"Successfully loaded: {os.path.basename(file_path)}")
            else:
                self.status_label.setText("Failed to parse URDF file")
                
        except Exception as e:
            error_msg = f"Error loading URDF file: {str(e)}"
            print(error_msg)
            QMessageBox.critical(self, "Error", error_msg)
            self.status_label.setText("Error occurred")
    
    def display_urdf_info(self):
        """显示URDF信息"""
        info = f"Robot Name: {self.parser.robot_name}\n"
        info += f"Links: {len(self.parser.links)}\n"
        info += f"Joints: {len(self.parser.joints)}\n\n"
        
        info += "Links:\n"
        for link in self.parser.links:
            info += f"  - {link['name']} ({len(link['visual'])} visual elements)\n"
        
        info += "\nJoints:\n"
        movable_count = 0
        for joint in self.parser.joints:
            joint_type = joint['type']
            if joint_type in ['revolute', 'continuous', 'prismatic']:
                movable_count += 1
                info += f"  - {joint['name']} ({joint_type}) [可控制]\n"
            else:
                info += f"  - {joint['name']} ({joint_type})\n"

        info += f"\n可控制关节数量: {movable_count}"
        
        self.info_text.setPlainText(info)
    
    def visualize_urdf(self):
        """可视化URDF"""
        if not (VTK_AVAILABLE and QVTK_AVAILABLE):
            return

        # 清除之前的actors
        for actor in self.vtk_actors:
            self.renderer.RemoveActor(actor)
        self.vtk_actors.clear()
        self.link_actors.clear()

        try:
            colors = [(0.8, 0.2, 0.2), (0.2, 0.8, 0.2), (0.2, 0.2, 0.8),
                     (0.8, 0.8, 0.2), (0.8, 0.2, 0.8), (0.2, 0.8, 0.8)]

            print(f"Starting visualization of {len(self.parser.links)} links...")

            for i, link in enumerate(self.parser.links):
                print(f"Processing link {i}: {link['name']} with {len(link['visual'])} visual elements")

                # 获取从根链接到当前链接的累积变换（使用初始关节角度）
                transform_chain = self.parser.get_transform_chain(link['name'], self.joint_values)
                cumulative_transform = self.parser.compute_cumulative_transform(transform_chain)

                print(f"  Transform chain for {link['name']}: {len(transform_chain)} transforms")

                # 为每个link创建actor列表
                link_actors = []

                for j, visual in enumerate(link['visual']):
                    print(f"  Visual element {j}: {visual}")

                    if visual['geometry']:
                        print(f"  Creating actor for geometry: {visual['geometry']}")
                        actor = self.create_geometry_actor(visual['geometry'])
                        if actor:
                            # 设置颜色
                            color = colors[i % len(colors)]
                            actor.GetProperty().SetColor(*color)

                            # 处理visual元素自身的origin变换
                            visual_origin = visual['origin']
                            if isinstance(visual_origin, dict):
                                xyz = visual_origin['xyz']
                                rpy = visual_origin['rpy']
                            else:
                                # 兼容旧格式
                                xyz = visual_origin[:3]
                                rpy = visual_origin[3:6]

                            # 创建visual的变换矩阵
                            visual_transform = self.parser.create_transform_matrix(xyz, rpy)

                            # 组合累积变换和visual变换
                            final_transform = np.dot(cumulative_transform, visual_transform)

                            # 应用变换到actor
                            transform = vtk.vtkTransform()
                            vtk_matrix = vtk.vtkMatrix4x4()
                            for row in range(4):
                                for col in range(4):
                                    vtk_matrix.SetElement(row, col, final_transform[row, col])

                            transform.SetMatrix(vtk_matrix)
                            actor.SetUserTransform(transform)

                            self.renderer.AddActor(actor)
                            self.vtk_actors.append(actor)

                            # 存储actor信息，用于后续更新
                            actor_info = {
                                'actor': actor,
                                'visual_transform': visual_transform
                            }
                            link_actors.append(actor_info)

                            print(f"  Successfully added actor for {link['name']}")
                        else:
                            print(f"  Failed to create actor for {link['name']}")
                    else:
                        print(f"  No geometry found for visual element {j}")

                # 存储link的所有actors
                if link_actors:
                    self.link_actors[link['name']] = link_actors

            print(f"Visualized {len(self.vtk_actors)} components")

        except Exception as e:
            print(f"Error in visualization: {e}")
            import traceback
            traceback.print_exc()
    
    def create_geometry_actor(self, geometry):
        """根据几何体类型创建VTK actor"""
        try:
            if geometry['type'] == 'box':
                return self.create_box_actor(geometry['size'])
            elif geometry['type'] == 'cylinder':
                return self.create_cylinder_actor(geometry['radius'], geometry['length'])
            elif geometry['type'] == 'sphere':
                return self.create_sphere_actor(geometry['radius'])
            elif geometry['type'] == 'mesh':
                return self.create_mesh_actor(geometry['filename'])
            else:
                return None
        except Exception as e:
            print(f"Error creating geometry actor: {e}")
            return None
    
    def create_box_actor(self, size):
        """创建立方体actor"""
        box = vtk.vtkCubeSource()
        box.SetXLength(size[0])
        box.SetYLength(size[1])
        box.SetZLength(size[2])
        
        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputConnection(box.GetOutputPort())
        
        actor = vtk.vtkActor()
        actor.SetMapper(mapper)
        
        return actor
        
    def create_cylinder_actor(self, radius, length):
        """创建圆柱体actor"""
        cylinder = vtk.vtkCylinderSource()
        cylinder.SetRadius(radius)
        cylinder.SetHeight(length)
        cylinder.SetResolution(20)
        
        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputConnection(cylinder.GetOutputPort())
        
        actor = vtk.vtkActor()
        actor.SetMapper(mapper)
        
        return actor
    
    def create_sphere_actor(self, radius):
        """创建球体actor"""
        sphere = vtk.vtkSphereSource()
        sphere.SetRadius(radius)
        sphere.SetThetaResolution(20)
        sphere.SetPhiResolution(20)
        
        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputConnection(sphere.GetOutputPort())
        
        actor = vtk.vtkActor()
        actor.SetMapper(mapper)
        
        return actor
    
    def create_mesh_actor(self, filename):
        """创建mesh actor"""
        try:
            print(f"Attempting to load mesh: {filename}")

            # 检查文件是否存在
            if not os.path.exists(filename):
                print(f"Mesh file not found: {filename}")
                return None

            if filename.lower().endswith('.stl'):
                reader = vtk.vtkSTLReader()
                reader.SetFileName(filename)
                reader.Update()
                print(f"Successfully loaded STL: {filename}")
            elif filename.lower().endswith('.obj'):
                reader = vtk.vtkOBJReader()
                reader.SetFileName(filename)
                reader.Update()
                print(f"Successfully loaded OBJ: {filename}")
            else:
                print(f"Unsupported mesh format: {filename}")
                return None

            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputConnection(reader.GetOutputPort())

            actor = vtk.vtkActor()
            actor.SetMapper(mapper)

            return actor
        except Exception as e:
            print(f"Error loading mesh {filename}: {e}")
            return None
        
    def reset_camera(self):
        """重置相机"""
        if VTK_AVAILABLE and QVTK_AVAILABLE and hasattr(self, 'renderer'):
            self.renderer.ResetCamera()
            self.vtk_widget.GetRenderWindow().Render()

def main():
    """主函数"""
    print("URDF Visualizer - Standalone Version")
    print("=" * 50)
    print(f"PyQt5: {'Available' if PYQT_AVAILABLE else 'Not Available'}")
    print(f"VTK: {'Available' if VTK_AVAILABLE else 'Not Available'}")
    print(f"VTK-Qt: {'Available' if QVTK_AVAILABLE else 'Not Available'}")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    try:
        window = URDFVisualizerStandalone()
        window.show()
        sys.exit(app.exec_())
    except Exception as e:
        print(f"Error starting application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
