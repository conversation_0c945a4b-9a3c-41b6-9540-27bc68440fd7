"""
日志工具模块
提供统一的日志记录功能
"""

import logging
import os
from datetime import datetime
from pathlib import Path


class CustomFormatter(logging.Formatter):
    """自定义日志格式化器"""
    
    def __init__(self):
        super().__init__()
        
        # 定义不同级别的颜色
        self.COLORS = {
            'DEBUG': '\033[36m',    # 青色
            'INFO': '\033[32m',     # 绿色
            'WARNING': '\033[33m',  # 黄色
            'ERROR': '\033[31m',    # 红色
            'CRITICAL': '\033[35m', # 紫色
            'RESET': '\033[0m'      # 重置
        }
        
        # 定义格式
        self.FORMATS = {
            logging.DEBUG: self._get_format('DEBUG'),
            logging.INFO: self._get_format('INFO'),
            logging.WARNING: self._get_format('WARNING'),
            logging.ERROR: self._get_format('ERROR'),
            logging.CRITICAL: self._get_format('CRITICAL'),
        }
    
    def _get_format(self, level: str) -> str:
        """获取指定级别的格式"""
        color = self.COLORS.get(level, '')
        reset = self.COLORS['RESET']
        
        return (f"{color}%(asctime)s - %(name)s - %(levelname)s{reset} - "
                f"%(message)s")
    
    def format(self, record):
        """格式化日志记录"""
        log_fmt = self.FORMATS.get(record.levelno)
        formatter = logging.Formatter(log_fmt, datefmt='%Y-%m-%d %H:%M:%S')
        return formatter.format(record)


def setup_logger(name: str, level: str = 'INFO', 
                log_file: str = None, console: bool = True) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        level: 日志级别
        log_file: 日志文件路径
        console: 是否输出到控制台
        
    Returns:
        配置好的日志记录器
    """
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))
    
    # 清除现有处理器
    logger.handlers.clear()
    
    # 文件处理器
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_path, encoding='utf-8')
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
    
    # 控制台处理器
    if console:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(CustomFormatter())
        logger.addHandler(console_handler)
    
    return logger


def get_logger(name: str) -> logging.Logger:
    """
    获取日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        日志记录器
    """
    return logging.getLogger(name)
