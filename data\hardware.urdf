<?xml version="1.0"?>
<robot name="linear_guide_system_v3_optimized">

  <!-- ***************************************************************** -->
  <!--                      材质与颜色定义 (可选)                           -->
  <!-- ***************************************************************** -->
  <material name="silver">
    <color rgba="0.75 0.75 0.75 1"/>
  </material>

  <material name="blue">
    <color rgba="0.1 0.1 0.8 1"/>
  </material>

  <!-- ***************************************************************** -->
  <!--                             连杆定义 (Links)                      -->
  <!-- ***************************************************************** -->

  <!-- 1. 基础连杆: 1500mm 直线导轨 -->
  <link name="guide_rail">
    <visual>
      <origin xyz="0.75 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="1.5 0.05 0.02"/>
      </geometry>
      <material name="silver"/>
    </visual>
    <collision>
      <origin xyz="0.75 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="1.5 0.05 0.02"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1"/>
      <inertia ixx="0.01" ixy="0" ixz="0" iyy="0.01" iyz="0" izz="0.01"/>
    </inertial>
  </link>

  <!-- 2. 滑块 1 -->
  <link name="slider_1">
    <visual>
      <geometry>
        <box size="0.1 0.08 0.04"/>
      </geometry>
      <material name="blue"/>
    </visual>
    <collision>
      <geometry>
        <box size="0.1 0.08 0.04"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.2"/>
      <inertia ixx="0.001" ixy="0" ixz="0" iyy="0.001" iyz="0" izz="0.001"/>
    </inertial>
  </link>

  <!-- 3. 滑块 2 -->
  <link name="slider_2">
    <visual>
      <geometry>
        <box size="0.1 0.08 0.04"/>
      </geometry>
      <material name="blue"/>
    </visual>
    <collision>
      <geometry>
        <box size="0.1 0.08 0.04"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.2"/>
      <inertia ixx="0.001" ixy="0" ixz="0" iyy="0.001" iyz="0" izz="0.001"/>
    </inertial>
  </link>

  <!-- 4. 滑块 3 -->
  <link name="slider_3">
    <visual>
      <geometry>
        <box size="0.1 0.08 0.04"/>
      </geometry>
      <material name="blue"/>
    </visual>
    <collision>
      <geometry>
        <box size="0.1 0.08 0.04"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.2"/>
      <inertia ixx="0.001" ixy="0" ixz="0" iyy="0.001" iyz="0" izz="0.001"/>
    </inertial>
  </link>


  <!-- ***************************************************************** -->
  <!--                             关节定义 (Joints)                      -->
  <!-- ***************************************************************** -->

  <!-- 关节 1: 连接导轨和滑块1 -->
  <joint name="rail_to_slider_1" type="prismatic">
    <parent link="guide_rail"/>
    <child link="slider_1"/>
    <!-- 初始位置: 250mm -->
    <origin xyz="0.25 0 0.03" rpy="0 0 0"/>
    <axis xyz="1 0 0"/>
    <!-- 优化后的行程限制: 400mm, 从初始位置前后各移动200mm -->
    <limit lower="-0.2" upper="0.2" effort="100" velocity="1.0"/>
  </joint>

  <!-- 关节 2: 连接导轨和滑块2 -->
  <joint name="rail_to_slider_2" type="prismatic">
    <parent link="guide_rail"/>
    <child link="slider_2"/>
    <!-- 初始位置: 750mm -->
    <origin xyz="0.75 0 0.03" rpy="0 0 0"/>
    <axis xyz="1 0 0"/>
    <!-- 优化后的行程限制: 400mm, 从初始位置前后各移动200mm -->
    <limit lower="-0.2" upper="0.2" effort="100" velocity="1.0"/>
  </joint>

  <!-- 关节 3: 连接导轨和滑块3 -->
  <joint name="rail_to_slider_3" type="prismatic">
    <parent link="guide_rail"/>
    <child link="slider_3"/>
    <!-- 初始位置: 1250mm -->
    <origin xyz="1.25 0 0.03" rpy="0 0 0"/>
    <axis xyz="1 0 0"/>
    <!-- 优化后的行程限制: 400mm, 从初始位置前后各移动200mm -->
    <limit lower="-0.2" upper="0.2" effort="100" velocity="1.0"/>
  </joint>

</robot>