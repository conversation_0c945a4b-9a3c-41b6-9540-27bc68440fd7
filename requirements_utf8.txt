﻿# 25AutoAssembly - 自动装配控制系统依赖包
# 适用于Anaconda虚拟环境: 25Assembly
# 优化版本以确保打包兼容性

# GUI框架
PyQt5>=5.15.0,<5.16.0    # Qt5图形界面框架 (稳定版本)

# 第三方依赖包 (打包优化版本)
watchdog>=3.0.0,<4.0.0   # 文件监控
numpy>=1.21.0,<2.0.0     # 数值计算 (避免2.x版本的打包问题)
netifaces>=0.11.0        # 网络接口信息获取

# 打包工具 (可选)
# pyinstaller>=4.10,<6.0  # 程序打包工具 (兼容性更好的版本)

# Python内置模块 (无需安装)
# socket      # TCP/IP通信
# threading   # 多线程
# json        # 配置管理
# logging     # 日志系统
# os          # 文件操作
# datetime    # 时间处理
# re          # 正则表达式
# pathlib     # 路径处理
