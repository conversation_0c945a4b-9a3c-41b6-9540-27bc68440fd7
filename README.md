# 25AutoAssembly

25AutoAssembly是一个自动装配控制系统，通过TCP连接到运动控制计算机，实现对多个硬件设备的精密控制。

## 🎯 功能特性

- 🖥️ **统一界面**: 集成所有功能的统一用户界面
- 📡 **TCP通信**: 连接到运动控制计算机 (************:8080)
- 🎮 **设备选择**: 可视化选择当前控制的设备 (1号六自由度台、2号六自由度台、3号六自由度台)
- 📊 **ZFR数据处理**: 自动处理偏心倾斜数据并生成运动指令
- 🎛️ **手动控制**: 精确的手动运动参数输入
- 🛡️ **安全保护**: 运动限制、紧急停止、异常处理
- 📝 **实时日志**: 完整的操作记录和状态监控

## 🏗️ 系统架构

```
25AutoAssembly (本机)
    ↓ TCP/IP (************:8080)
运动控制计算机 (************)
    ↓ 运动控制软件
硬件设备:
    ├─ 1号六自由度台 (***********)
    ├─ 2号六自由度台 (***********)
    └─ 3号六自由度台 (***********)
```

## 💻 系统要求

- Python 3.7+
- PyQt5
- NumPy
- Watchdog

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 启动程序
```bash
# 方式1: 完整启动 (包含环境检查)
python main.py

# 方式2: 快速启动
python run.py
```

### 3. 使用界面
1. **控制面板**选项卡:
   - 点击"连接"按钮连接到运动控制计算机
   - 选择要控制的设备 (副镜/主镜/三镜/宏观)
   - 手动输入运动参数或处理ZFR数据
   - 查看实时日志

2. **文件设置**选项卡:
   - 设置结果文件目录
   - 手动加载ZFR数据文件

## 📦 项目结构

```
25AutoAssembly/
├── main.py                     # 主程序入口 (完整版)
├── run.py                      # 快速启动脚本
├── gui/
│   └── motion_control_ui.py    # 运动控制界面
├── device_communication/
│   └── tcp_motion_controller.py # TCP运动控制器
├── data_models/
│   └── zfr_data.py            # ZFR数据模型
├── file_handler/              # 文件处理模块
├── config/                    # 配置文件
├── utils/                     # 工具函数
├── resource/                  # 资源文件 (参考实现)
├── logs/                      # 日志文件
├── API/                       # 硬件API库
├── PROJECT_FINAL_SUMMARY.md   # 项目完整总结
└── TCP_INTEGRATION_COMPLETE.md # TCP集成报告
```



## ⚙️ 配置

编辑 `config/config.json` 来配置系统参数：

```json
{
    "file": {
        "result_directory": "./resource",
        "auto_reload": true
    },
    "motion": {
        "scale_factor": 1.0,
        "max_movement": 10.0
    },
    "tcp": {
        "host": "************",
        "port": 8080
    }
}
```

## 🔧 部署要求

### 运动控制计算机
- IP地址: ************
- 监听端口: 8080
- 支持JSON格式的TCP指令
- 能够解析`target_device_ip`并转发到对应硬件

### 硬件设备IP配置
- 1号六自由度台: ***********
- 2号六自由度台: ***********
- 3号六自由度台: ***********

## 📋 TCP通信协议

### 指令格式
```json
{
    "target_device_ip": "***********",
    "command_type": "move_relative",
    "x": 0.001, "y": 0.001, "z": 0.001,
    "timestamp": **********.789
}
```

### 支持的命令
- `init` - 客户端初始化
- `move_relative` - 相对运动
- `move_absolute` - 绝对运动
- `stop` - 停止运动
- `query` - 查询状态
- `heartbeat` - 心跳检测

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License
