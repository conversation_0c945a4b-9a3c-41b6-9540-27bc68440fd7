"""
设备配置管理模块
负责加载和管理设备映射配置
"""

import json
import os
import logging
from typing import Dict, Any, Optional, Tuple
from enum import Enum


class DeviceConfigManager:
    """设备配置管理器"""
    
    def __init__(self, config_file: str = "config/device_mapping.json"):
        """
        初始化设备配置管理器
        
        Args:
            config_file: 设备映射配置文件路径
        """
        self.config_file = config_file
        self._config: Dict[str, Any] = {}
        self._device_mapping: Dict[str, Dict[str, str]] = {}
        self._ip_to_device_id: Dict[str, str] = {}
        self._device_type_mapping: Dict[str, str] = {}
        self.load_config()
    
    def load_config(self) -> None:
        """加载设备配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self._config = json.load(f)
                self._build_mappings()
                logging.info(f"设备配置文件加载成功: {self.config_file}")
            else:
                logging.warning(f"设备配置文件不存在: {self.config_file}")
                self._create_default_config()
        except Exception as e:
            logging.error(f"设备配置文件加载失败: {e}")
            self._create_default_config()
    
    def _build_mappings(self) -> None:
        """构建设备映射关系"""
        device_mapping = self._config.get('device_mapping', {})
        
        # 构建设备ID到配置的映射
        self._device_mapping = device_mapping
        
        # 构建IP到设备ID的反向映射
        self._ip_to_device_id = {}
        for device_id, config in device_mapping.items():
            ip = config.get('ip')
            if ip:
                self._ip_to_device_id[ip] = device_id
        
        # 构建设备类型映射
        self._device_type_mapping = {}
        for device_id, config in device_mapping.items():
            device_type = config.get('device_type')
            if device_type:
                self._device_type_mapping[device_type] = device_id
    
    def get_device_ip(self, device_id: str) -> Optional[str]:
        """
        根据设备ID获取IP地址
        
        Args:
            device_id: 设备ID (如 'device1')
            
        Returns:
            设备IP地址
        """
        device_config = self._device_mapping.get(device_id, {})
        return device_config.get('ip')
    
    def get_device_id_by_ip(self, ip: str) -> Optional[str]:
        """
        根据IP地址获取设备ID
        
        Args:
            ip: IP地址
            
        Returns:
            设备ID
        """
        return self._ip_to_device_id.get(ip)
    
    def get_device_type(self, device_id: str) -> Optional[str]:
        """
        根据设备ID获取设备类型
        
        Args:
            device_id: 设备ID
            
        Returns:
            设备类型 (如 'MAINMIRROR')
        """
        device_config = self._device_mapping.get(device_id, {})
        return device_config.get('device_type')
    
    def get_device_id_by_type(self, device_type: str) -> Optional[str]:
        """
        根据设备类型获取设备ID
        
        Args:
            device_type: 设备类型 (如 'MAINMIRROR')
            
        Returns:
            设备ID
        """
        return self._device_type_mapping.get(device_type)
    
    def get_device_display_name(self, device_id: str) -> str:
        """
        根据设备ID获取显示名称
        
        Args:
            device_id: 设备ID
            
        Returns:
            设备显示名称
        """
        device_config = self._device_mapping.get(device_id, {})
        return device_config.get('display_name', device_id)
    
    def get_all_devices(self) -> Dict[str, Dict[str, str]]:
        """获取所有设备配置"""
        return self._device_mapping.copy()
    
    def get_device_ip_mapping(self) -> Dict[str, str]:
        """
        获取设备ID到IP的映射字典
        
        Returns:
            {device_id: ip} 格式的映射字典
        """
        mapping = {}
        for device_id, config in self._device_mapping.items():
            ip = config.get('ip')
            if ip:
                mapping[device_id] = ip
        return mapping
    
    def get_ip_device_mapping(self) -> Dict[str, str]:
        """
        获取IP到设备ID的映射字典
        
        Returns:
            {ip: device_id} 格式的映射字典
        """
        return self._ip_to_device_id.copy()
    
    def get_motion_control_server(self) -> Tuple[str, int]:
        """
        获取运动控制服务器配置
        
        Returns:
            (ip, port) 元组
        """
        network_settings = self._config.get('network_settings', {})
        server_config = network_settings.get('motion_control_server', {})
        ip = server_config.get('ip', '************')
        port = server_config.get('port', 8080)
        return ip, port
    
    def validate_device_id(self, device_id: str) -> bool:
        """
        验证设备ID是否有效
        
        Args:
            device_id: 设备ID
            
        Returns:
            是否有效
        """
        return device_id in self._device_mapping
    
    def validate_ip(self, ip: str) -> bool:
        """
        验证IP地址是否在配置中
        
        Args:
            ip: IP地址
            
        Returns:
            是否有效
        """
        return ip in self._ip_to_device_id
    
    def _create_default_config(self) -> None:
        """创建默认设备配置"""
        self._config = {
            "device_mapping": {
                "device1": {
                    "ip": "***********",
                    "device_type": "MAINMIRROR",
                    "display_name": "2号六自由度台",
                    "description": "主镜控制设备"
                },
                "device2": {
                    "ip": "***********", 
                    "device_type": "TERTIARY",
                    "display_name": "3号六自由度台",
                    "description": "三镜控制设备"
                },
                "device3": {
                    "ip": "***********",
                    "device_type": "SUBMIRROR", 
                    "display_name": "1号六自由度台",
                    "description": "副镜控制设备"
                }
            },
            "network_settings": {
                "motion_control_server": {
                    "ip": "************",
                    "port": 8080,
                    "timeout": 5.0
                }
            },
            "device_types": {
                "MAINMIRROR": {
                    "display_name": "主镜",
                    "priority": 1
                },
                "TERTIARY": {
                    "display_name": "三镜", 
                    "priority": 2
                },
                "SUBMIRROR": {
                    "display_name": "副镜",
                    "priority": 3
                }
            }
        }
        self._build_mappings()
        self.save_config()
    
    def save_config(self) -> None:
        """保存设备配置文件"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=4, ensure_ascii=False)
            logging.info(f"设备配置文件保存成功: {self.config_file}")
        except Exception as e:
            logging.error(f"设备配置文件保存失败: {e}")


# 全局设备配置实例
device_config = DeviceConfigManager()