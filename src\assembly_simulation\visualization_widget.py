#!/usr/bin/env python3
"""
25AutoAssembly 3D可视化组件模块

基于PyVista QtInteractor实现3D窗口嵌入PyQt5，提供机器人模型3D可视化和交互功能
参考自USMC项目的visualization_widget_pyvista.py实现
"""

import sys
import logging
from typing import Optional, Dict, List, Tuple, Any
import numpy as np

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QFrame, QSizePolicy, QApplication, QMessageBox, QGroupBox,
    QDoubleSpinBox, QGridLayout, QSplitter
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, pyqtSlot
from PyQt5.QtGui import QFont

# 获取日志记录器
logger = logging.getLogger(__name__)

# PyVista imports
try:
    import pyvista as pv
    logger.info(f"PyVista {pv.__version__} 导入成功")

    # 尝试导入QtInteractor，优先使用pyvistaqt
    try:
        import pyvistaqt as pvqt
        QtInteractor = pvqt.QtInteractor
        logger.info(f"使用PyVistaQt {pvqt.__version__}.QtInteractor")
    except ImportError as e1:
        logger.warning(f"PyVistaQt导入失败: {e1}")
        try:
            from pyvista.plotting.qt_plotting import QtInteractor
            logger.info("使用PyVista内置QtInteractor")
        except ImportError as e2:
            logger.error(f"PyVista内置QtInteractor导入失败: {e2}")
            raise ImportError(
                "无法导入QtInteractor。请尝试以下解决方案：\n"
                "1. pip install pyvistaqt\n"
                "2. 或确保PyVista版本支持Qt集成\n"
                f"当前PyVista版本: {pv.__version__}"
            )

    PYVISTA_AVAILABLE = True
    logger.info("PyVista环境检查完成，所有组件可用")

except ImportError as e:
    logger.error(f"PyVista导入失败: {e}")
    PYVISTA_AVAILABLE = False
    QtInteractor = None

# 导入项目模块
# 暂时不导入SE3，避免版本兼容性问题
SE3 = None


class VisualizationWidget(QWidget):
    """
    25AutoAssembly 3D可视化组件类 - 基于PyVista方案
    
    提供完整的3D可视化功能，包括：
    - PyVista QtInteractor嵌入PyQt5
    - 3D场景初始化和管理
    - 坐标系显示和管理
    - 相机控制和视角管理
    - 几何体添加/移除/更新
    - 完整的鼠标交互支持
    - 硬件加速渲染
    """
    
    # 自定义信号
    scene_initialized = pyqtSignal()  # 场景初始化完成信号
    camera_view_changed = pyqtSignal(dict)  # 相机视角变化信号
    geometry_added = pyqtSignal(str)  # 几何体添加信号
    geometry_removed = pyqtSignal(str)  # 几何体移除信号
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        初始化PyVista可视化组件
        
        Args:
            parent: 父窗口，默认为None
        """
        super().__init__(parent)
        
        # 检查PyVista可用性
        if not PYVISTA_AVAILABLE:
            error_msg = (
                "PyVista库未安装或导入失败。\n\n"
                "请检查以下步骤：\n"
                "1. 确保已激活正确的conda环境\n"
                "2. 运行: pip install pyvista[all]\n"
                "3. 运行: pip install pyvistaqt\n"
                "4. 重启应用程序\n\n"
                "如果问题仍然存在，请检查Python环境配置。"
            )
            self._show_error_message("PyVista不可用", error_msg)
            return
        
        # 组件状态
        self.is_initialized = False
        self.plotter: Optional[QtInteractor] = None
        self.geometries: Dict[str, Any] = {}
        self.coordinate_frames: List[str] = []
        
        # 相机预设视角
        self.camera_presets = {
            'isometric': [1, 1, 1],
            'front': [0, 0, 1],
            'back': [0, 0, -1],
            'left': [-1, 0, 0],
            'right': [1, 0, 0],
            'top': [0, 1, 0],
            'bottom': [0, -1, 0]
        }
        
        # 初始化UI
        self._init_ui()
        
        logger.info("PyVista可视化组件初始化完成")
    
    def _init_ui(self):
        """初始化用户界面 - PyVista方案"""
        # 创建主布局（容器布局）
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建PyVista QtInteractor
        try:
            self.plotter = QtInteractor(self)
            self.plotter.interactor.setMinimumHeight(300)  # 设置3D显示区域最小高度

            # 添加到主布局
            main_layout.addWidget(self.plotter.interactor)

            # 设置窗口属性
            self.setMinimumSize(600, 400)
            self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

            logger.info("PyVista QtInteractor创建成功")

        except Exception as e:
            logger.error(f"创建PyVista QtInteractor失败: {e}")
            self._show_error_message("3D渲染器初始化失败", str(e))
            return

        logger.info("3D可视化UI初始化完成（PyVista方案）")

    def initialize_scene(self):
        """初始化3D场景"""
        if not self.plotter:
            logger.error("PyVista plotter未初始化")
            return False

        try:
            # 设置背景颜色（专业深色主题）
            self.plotter.background_color = (0.2, 0.3, 0.4)

            # 添加坐标系
            self.add_coordinate_frame("world", size=1.0)

            # 添加网格地面
            self.add_grid_ground()

            # 设置默认相机位置（等轴测视角）
            self.set_camera_view('isometric')

            # 启用交互控制
            self.plotter.enable_trackball_style()

            # 添加交互式坐标轴控件（左下角）
            self.add_orientation_widget()

            # 强制渲染一次
            self.render_scene()

            self.is_initialized = True
            self.scene_initialized.emit()

            logger.info("3D场景初始化完成")
            return True

        except Exception as e:
            logger.error(f"3D场景初始化失败: {e}")
            return False

    def render_scene(self):
        """强制渲染3D场景"""
        if not self.plotter:
            return

        try:
            # 强制渲染
            self.plotter.render()
            logger.debug("3D场景渲染完成")
        except Exception as e:
            logger.error(f"3D场景渲染失败: {e}")

    def show_widget(self):
        """显示widget并渲染场景"""
        try:
            # 确保widget可见
            self.show()

            # 强制渲染场景
            self.render_scene()

            logger.info("PyVista widget显示完成")
        except Exception as e:
            logger.error(f"显示PyVista widget失败: {e}")

    def add_grid_ground(self):
        """添加网格地面以提供空间参考"""
        if not self.plotter:
            return

        try:
            # 创建网格地面 - 在XY平面（Z=0）
            grid_size = 4  # 网格总尺寸（减小以适配镜头显示）
            grid_resolution = 12  # 网格分辨率（相应调整保持合理密度）

            # 创建平面网格
            ground_plane = pv.Plane(
                center=(0, 0, 0),  # 位于原点
                direction=(0, 0, 1),  # 法向量指向Z轴正方向
                i_size=grid_size,  # X方向尺寸
                j_size=grid_size,  # Y方向尺寸
                i_resolution=grid_resolution,  # X方向分辨率
                j_resolution=grid_resolution   # Y方向分辨率
            )

            # 添加网格地面到场景
            self.add_geometry(
                "grid_ground",
                ground_plane,
                color='lightgray',  # 浅灰色，与深色背景协调
                opacity=0.3,       # 适当透明度，不干扰其他内容
                show_edges=True,   # 显示网格线
                edge_color='white', # 白色网格线，清晰可见
                line_width=1       # 适中的线宽
            )

            logger.info("网格地面添加成功")

        except Exception as e:
            logger.error(f"添加网格地面失败: {e}")

    def add_coordinate_frame(self, name: str, pose=None, size: float = 1.0):
        """
        添加坐标系显示
        
        Args:
            name: 坐标系名称
            pose: 坐标系位姿，默认为原点
            size: 坐标系大小
        """
        if not self.plotter:
            return
        
        try:
            # 创建坐标系几何体
            if pose is None:
                # 默认在原点
                origin = [0, 0, 0]
            else:
                # 从SE3提取位置
                origin = pose.t.tolist()
            
            # 添加坐标轴
            # X轴 - 红色
            x_axis = pv.Line([origin[0], origin[1], origin[2]], 
                           [origin[0] + size, origin[1], origin[2]])
            self.plotter.add_mesh(x_axis, color='red', line_width=3, name=f"{name}_x_axis")
            
            # Y轴 - 绿色
            y_axis = pv.Line([origin[0], origin[1], origin[2]], 
                           [origin[0], origin[1] + size, origin[2]])
            self.plotter.add_mesh(y_axis, color='green', line_width=3, name=f"{name}_y_axis")
            
            # Z轴 - 蓝色
            z_axis = pv.Line([origin[0], origin[1], origin[2]], 
                           [origin[0], origin[1], origin[2] + size])
            self.plotter.add_mesh(z_axis, color='blue', line_width=3, name=f"{name}_z_axis")
            
            # 记录坐标系
            self.coordinate_frames.append(name)
            
            logger.info(f"坐标系 '{name}' 添加成功")
            
        except Exception as e:
            logger.error(f"添加坐标系失败: {e}")
    
    def set_camera_view(self, view: str):
        """
        设置相机视角
        
        Args:
            view: 视角名称 ('isometric', 'front', 'back', 'left', 'right', 'top', 'bottom')
        """
        if not self.plotter or view not in self.camera_presets:
            return
        
        try:
            position = self.camera_presets[view]
            self.plotter.camera_position = position
            self.camera_view_changed.emit({'view': view, 'position': position})
            
            logger.info(f"相机视角设置为: {view}")
            
        except Exception as e:
            logger.error(f"设置相机视角失败: {e}")

    def add_orientation_widget(self):
        """
        添加交互式坐标轴控件到3D场景左下角
        """
        if not self.plotter:
            logger.warning("PyVista plotter未初始化，无法添加坐标轴控件")
            return

        try:
            # 添加坐标轴控件
            self.plotter.add_axes(
                viewport=(0, 0, 0.2, 0.2),  # 左下角位置，占20%×20%区域
                line_width=5,               # 轴线宽度，使其清晰可见
                cone_radius=0.6,           # 箭头锥体半径
                shaft_length=0.7,          # 轴线长度
                tip_length=0.3,            # 箭头长度
                ambient=0.5,               # 环境光设置
                label_size=(0.4, 0.16)     # X、Y、Z标签大小
            )

            logger.info("✅ 交互式坐标轴控件添加成功")

        except Exception as e:
            logger.warning(f"⚠️ 坐标轴控件添加失败: {e}")

    def add_geometry(self, name: str, geometry: Any, **kwargs):
        """
        添加几何体到场景
        
        Args:
            name: 几何体名称
            geometry: PyVista几何体对象
            **kwargs: 渲染参数（颜色、透明度等）
        """
        if not self.plotter:
            return
        
        try:
            self.plotter.add_mesh(geometry, name=name, **kwargs)
            self.geometries[name] = geometry
            self.geometry_added.emit(name)
            
            logger.info(f"几何体 '{name}' 添加成功")
            
        except Exception as e:
            logger.error(f"添加几何体失败: {e}")
    
    def remove_geometry(self, name: str):
        """
        移除几何体
        
        Args:
            name: 几何体名称
        """
        if not self.plotter or name not in self.geometries:
            return
        
        try:
            self.plotter.remove_actor(name)
            del self.geometries[name]
            self.geometry_removed.emit(name)
            
            logger.info(f"几何体 '{name}' 移除成功")
            
        except Exception as e:
            logger.error(f"移除几何体失败: {e}")

    def _show_error_message(self, title: str, message: str):
        """显示错误消息"""
        try:
            QMessageBox.critical(self, title, message)
        except Exception:
            # 如果GUI不可用，至少记录错误
            logger.error(f"{title}: {message}")
    
    def cleanup(self):
        """
        清理PyVista资源 - 解决退出时的渲染错误
        """
        try:
            print("🧹 [PyVista] 开始清理PyVista资源...")

            # 清理几何体
            if hasattr(self, 'geometries') and self.geometries:
                print(f"🧹 [PyVista] 清理 {len(self.geometries)} 个几何体...")
                self.geometries.clear()

            # 清理坐标系
            if hasattr(self, 'coordinate_frames') and self.coordinate_frames:
                print(f"🧹 [PyVista] 清理 {len(self.coordinate_frames)} 个坐标系...")
                self.coordinate_frames.clear()

            # 关闭PyVista plotter
            if hasattr(self, 'plotter') and self.plotter:
                print("🧹 [PyVista] 关闭PyVista plotter...")
                try:
                    self.plotter.close()
                    self.plotter = None
                except Exception as e:
                    # 忽略关闭过程中的错误
                    pass

            print("✅ [PyVista] PyVista资源清理完成")

        except Exception as e:
            print(f"⚠️ [PyVista] PyVista清理过程中出现错误（通常无害）: {e}")

    def closeEvent(self, event):
        """窗口关闭事件处理"""
        try:
            # 清理PyVista资源
            self.cleanup()
            logger.info("VisualizationWidget关闭完成（PyVista方案）")

        except Exception as e:
            logger.error(f"关闭VisualizationWidget时出错: {e}")

        super().closeEvent(event)
