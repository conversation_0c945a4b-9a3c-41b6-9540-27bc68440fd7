# 设备配置系统使用指南

## 概述

25AutoAssembly项目现已支持可配置的设备映射关系，不再使用硬编码的IP地址和设备ID映射。所有设备配置都通过配置文件进行管理。

## 配置文件结构

### 主配置文件: `config/device_mapping.json`

```json
{
    "device_mapping": {
        "device1": {
            "ip": "***********",
            "device_type": "MAINMIRROR",
            "display_name": "主镜",
            "description": "主镜控制设备"
        },
        "device2": {
            "ip": "***********", 
            "device_type": "TERTIARY",
            "display_name": "三镜",
            "description": "三镜控制设备"
        },
        "device3": {
            "ip": "***********",
            "device_type": "SUBMIRROR", 
            "display_name": "副镜",
            "description": "副镜控制设备"
        }
    },
    "network_settings": {
        "motion_control_server": {
            "ip": "************",
            "port": 8080,
            "timeout": 5.0
        }
    },
    "device_types": {
        "MAINMIRROR": {
            "display_name": "主镜",
            "priority": 1
        },
        "TERTIARY": {
            "display_name": "三镜", 
            "priority": 2
        },
        "SUBMIRROR": {
            "display_name": "副镜",
            "priority": 3
        }
    }
}
```

## 当前映射关系

根据用户要求，当前的设备映射关系为：

| 设备ID | IP地址 | 设备类型 | 显示名称 |
|--------|--------|----------|----------|
| device1 | *********** | MAINMIRROR | 主镜 |
| device2 | *********** | TERTIARY | 三镜 |
| device3 | *********** | SUBMIRROR | 副镜 |

## 配置管理API

### DeviceConfigManager 类

位置: `config/device_config.py`

#### 主要方法

1. **获取设备IP地址**
   ```python
   device_config.get_device_ip("device1")  # 返回 "***********"
   ```

2. **根据IP获取设备ID**
   ```python
   device_config.get_device_id_by_ip("***********")  # 返回 "device1"
   ```

3. **获取设备类型**
   ```python
   device_config.get_device_type("device1")  # 返回 "MAINMIRROR"
   ```

4. **根据设备类型获取设备ID**
   ```python
   device_config.get_device_id_by_type("MAINMIRROR")  # 返回 "device1"
   ```

5. **获取设备显示名称**
   ```python
   device_config.get_device_display_name("device1")  # 返回 "主镜"
   ```

6. **获取运动控制服务器配置**
   ```python
   ip, port = device_config.get_motion_control_server()  # 返回 ("************", 8080)
   ```

## 代码修改说明

### 1. DeviceType 枚举类修改

**修改前** (硬编码IP):
```python
class DeviceType(Enum):
    SUBMIRROR = ("1号六自由度台", "***********")
    MAINMIRROR = ("2号六自由度台", "***********")
    TERTIARY = ("3号六自由度台", "***********")
```

**修改后** (动态配置):
```python
class DeviceType(Enum):
    SUBMIRROR = ("副镜", "SUBMIRROR")
    MAINMIRROR = ("主镜", "MAINMIRROR")
    TERTIARY = ("三镜", "TERTIARY")
    
    @property
    def ip_address(self) -> str:
        """动态获取IP地址（从配置文件）"""
        from config.device_config import device_config
        device_id = device_config.get_device_id_by_type(self.type_name)
        if device_id:
            return device_config.get_device_ip(device_id) or "***********"
        return "***********"
```

### 2. TCP控制器修改

- 移除硬编码的 `device_ips` 字典
- 使用 `device.ip_address` 动态获取IP地址
- 使用配置管理器进行设备ID和IP的映射

### 3. GUI界面修改

- 移除硬编码的设备映射字典
- 使用配置管理器获取当前设备ID
- 动态获取运动控制服务器地址

## 如何修改设备配置

### 1. 修改设备IP地址

编辑 `config/device_mapping.json` 文件：

```json
{
    "device_mapping": {
        "device1": {
            "ip": "************0",  // 修改IP地址
            "device_type": "MAINMIRROR",
            "display_name": "主镜",
            "description": "主镜控制设备"
        }
    }
}
```

### 2. 添加新设备

在 `device_mapping` 中添加新的设备条目：

```json
{
    "device_mapping": {
        "device4": {
            "ip": "***********",
            "device_type": "NEWDEVICE",
            "display_name": "新设备",
            "description": "新增设备"
        }
    }
}
```

### 3. 修改运动控制服务器地址

```json
{
    "network_settings": {
        "motion_control_server": {
            "ip": "************",  // 修改服务器IP
            "port": 9090,          // 修改端口
            "timeout": 10.0
        }
    }
}
```

## 配置验证

使用提供的测试脚本验证配置是否正确：

```bash
python test_device_config.py
```

测试脚本会验证：
- 配置文件加载
- 设备ID到IP的映射
- IP到设备ID的反向映射
- 设备类型映射
- DeviceType动态IP获取
- 运动控制服务器配置
- 配置验证功能

## 优势

1. **灵活性**: 无需修改代码即可更改设备配置
2. **可维护性**: 集中管理所有设备配置
3. **可扩展性**: 轻松添加新设备或修改现有设备
4. **一致性**: 统一的配置管理接口
5. **验证性**: 内置配置验证功能

## 注意事项

1. 修改配置文件后需要重启应用程序
2. 确保IP地址不冲突
3. 设备类型名称必须与DeviceType枚举一致
4. 配置文件格式必须为有效的JSON

## 故障排除

1. **配置文件不存在**: 系统会自动创建默认配置
2. **JSON格式错误**: 检查配置文件的JSON语法
3. **设备ID重复**: 确保每个设备ID唯一
4. **IP地址冲突**: 确保每个设备使用不同的IP地址

通过这个配置系统，25AutoAssembly项目现在具备了更好的灵活性和可维护性。