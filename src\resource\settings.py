# !/usr/bin/python3
# -*- coding: utf-8 -*-

import configparser
import logging
import os

import chardet
from sys import exit

__author__ = "zdy"

try:
    # 配置文件解析
    config = configparser.ConfigParser()
    with open('common/settings.ini', 'rb') as f:
        content = f.read()
        enc = chardet.detect(content)["encoding"]
    config.read('common/settings.ini', encoding=enc)

    log_level = config.getint('default', 'LOG_LEVEL')

    is_debug = config.getboolean('default', 'is_debug')
    SERVE_IP = config.get('default', 'SERVE_IP')
    A_IP = config.get('default', 'A_IP')
    B_IP = config.get('default', 'B_IP')

except Exception as err_info:
    logging.error('parse config fail, error = {}'.format(err_info))
    exit(0)
